import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import { createAppRouter } from './router'
import cachedAuthService from './services/CachedAuthService'
import routeCache from './utils/RouteCache'

/**
 * 缓存增强的应用初始化 - 防闪烁机制
 */
async function initializeApp() {
  console.log('App: 开始缓存增强的应用初始化')

  // 1. 缓存预热 - 立即获取认证状态（优先使用缓存）
  const authState = cachedAuthService.warmupCache()

  console.log('App: 缓存认证状态检查', {
    isAuthenticated: authState.isAuthenticated,
    fromCache: authState.fromCache,
    user: authState.user?.name,
    userRole: authState.user?.role
  })

  // 2. 根据缓存的认证状态创建路由实例
  const router = createAppRouter(authState.isAuthenticated)

  // 3. 创建 Vue 实例
  const app = createApp(App)
  const pinia = createPinia()

  // 注册所有图标
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }

  app.use(pinia)
  app.use(router)
  app.use(ElementPlus)

  // 4. 如果有有效认证信息，初始化认证状态
  if (authState.isAuthenticated) {
    const { useAuthStore } = await import('./stores/auth')
    const authStore = useAuthStore()

    // 使用缓存的数据初始化
    authStore.user = authState.user
    authStore.token = authState.token
    authStore.initialized = true

    console.log('App: 已登录用户，使用缓存初始化认证状态')

    // 异步验证缓存的有效性（不阻塞页面渲染）
    cachedAuthService.validateAndUpdateCache().then(validatedState => {
      if (!validatedState.isAuthenticated && authState.isAuthenticated) {
        console.log('App: 缓存验证失败，需要重新登录')
        window.location.reload()
      }
    }).catch(error => {
      console.warn('App: 异步验证失败', error)
    })
  } else {
    console.log('App: 未登录用户，跳过认证状态初始化')
  }

  // 5. 挂载应用
  app.mount('#app')

  // 6. 输出缓存统计信息
  const cacheStats = cachedAuthService.getCacheStats()
  console.log('App: 应用挂载完成，缓存统计', cacheStats)
}

// 启动应用
initializeApp().catch(error => {
  console.error('App: 应用初始化失败', error)
  routeCache.clearAllCache()
  setTimeout(() => window.location.reload(), 1000)
})
