{"version": 3, "file": "upload-dragger2.js", "sources": ["../../../../../../packages/components/upload/src/upload-dragger.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[ns.b('dragger'), ns.is('dragover', dragover)]\"\n    @drop.prevent=\"onDrop\"\n    @dragover.prevent=\"onDragover\"\n    @dragleave.prevent=\"onDragleave\"\n  >\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { inject, ref } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport { throwError } from '@element-plus/utils/error'\nimport { uploadContextKey } from './constants'\nimport { uploadDraggerEmits, uploadDraggerProps } from './upload-dragger'\n\nimport type { UploadRawFile } from './upload'\n\nconst COMPONENT_NAME = 'ElUploadDrag'\n\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\ndefineProps(uploadDraggerProps)\nconst emit = defineEmits(uploadDraggerEmits)\n\nconst uploaderContext = inject(uploadContextKey)\nif (!uploaderContext) {\n  throwError(\n    COMPONENT_NAME,\n    'usage: <el-upload><el-upload-dragger /></el-upload>'\n  )\n}\n\nconst ns = useNamespace('upload')\nconst dragover = ref(false)\nconst disabled = useFormDisabled()\n\nconst onDrop = (e: DragEvent) => {\n  if (disabled.value) return\n  dragover.value = false\n\n  e.stopPropagation()\n\n  const files = Array.from(e.dataTransfer!.files) as UploadRawFile[]\n  const items = e.dataTransfer!.items || []\n  files.forEach((file, index) => {\n    const item = items[index]\n    const entry = item?.webkitGetAsEntry?.()\n    if (entry) {\n      file.isDirectory = entry.isDirectory\n    }\n  })\n  emit('file', files)\n}\n\nconst onDragover = () => {\n  if (!disabled.value) dragover.value = true\n}\n\nconst onDragleave = (e: DragEvent) => {\n  if (!(e.currentTarget as Element).contains(e.relatedTarget as Element))\n    dragover.value = false\n}\n</script>\n"], "names": ["inject", "uploadContextKey", "throwError", "useNamespace", "ref", "useFormDisabled"], "mappings": ";;;;;;;;;;;;;uCAuBc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;AAKA,IAAM,MAAA,eAAA,GAAkBA,WAAOC,0BAAgB,CAAA,CAAA;AAC/C,IAAA,IAAI,CAAC,eAAiB,EAAA;AACpB,MAAAC,gBAAA,CAAA,cAAA,EAAA,qDAAA,CAAA,CAAA;AAAA,KACE;AAAA,IACA,MAAA,EAAA,GAAAC,kBAAA,CAAA,QAAA,CAAA,CAAA;AAAA,IACF,MAAA,QAAA,GAAAC,OAAA,CAAA,KAAA,CAAA,CAAA;AAAA,IACF,MAAA,QAAA,GAAAC,kCAAA,EAAA,CAAA;AAEA,IAAM,MAAA;AACN,MAAM,IAAA,QAAA,CAAA;AACN,QAAA;AAEA,MAAM,QAAA,CAAA,KAAU,GAAiB,KAAA,CAAA;AAC/B,MAAA,CAAA,CAAA,eAAoB,EAAA,CAAA;AACpB,MAAA,MAAA,KAAiB,GAAA,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAEjB,MAAA,MAAkB,KAAA,GAAA,CAAA,CAAA,YAAA,CAAA,KAAA,IAAA,EAAA,CAAA;AAElB,MAAA,KAAA,CAAM,OAAQ,CAAA,CAAA,IAAA,EAAW,KAAA;AACzB,QAAA,IAAM,EAAQ,CAAA;AACd,QAAM,MAAA,IAAA,GAAS,KAAA,CAAM,KAAU,CAAA,CAAA;AAC7B,QAAM,MAAA,KAAA,SAAkB,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,gBAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AACxB,QAAM,IAAA,KAAA,EAAA;AACN,UAAA,IAAW,CAAA,WAAA,GAAA,KAAA,CAAA,WAAA,CAAA;AACT,SAAA;AAAyB,OAC3B,CAAA,CAAA;AAAA,MACF,IAAC,CAAA,MAAA,EAAA,KAAA,CAAA,CAAA;AACD,KAAA,CAAA;AAAkB,IACpB,MAAA,UAAA,GAAA,MAAA;AAEA,MAAA,IAAM,eAAmB;AACvB,QAAA,QAAK,CAAA,KAAgB,GAAA,IAAA,CAAA;AAAiB,KACxC,CAAA;AAEA,IAAM,MAAA,WAAA,GAAc,CAAC,CAAiB,KAAA;AACpC,MAAA,IAAI,CAAE,CAAA,CAAE,aAA0B,CAAA,QAAA,CAAS,EAAE,aAAwB,CAAA;AACnE,QAAA,QAAA,CAAS,KAAQ,GAAA,KAAA,CAAA;AAAA,KACrB,CAAA;;;;;;;;;;;;;;;;;"}