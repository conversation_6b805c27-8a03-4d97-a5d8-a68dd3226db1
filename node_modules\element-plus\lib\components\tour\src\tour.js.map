{"version": 3, "file": "tour.js", "sources": ["../../../../../../packages/components/tour/src/tour.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  iconPropType,\n  isBoolean,\n  isNumber,\n} from '@element-plus/utils'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { teleportProps } from '@element-plus/components/teleport'\nimport { tourContentProps } from './content'\n\nimport type {\n  CSSProperties,\n  ExtractPropTypes,\n  __ExtractPublicPropTypes,\n} from 'vue'\nimport type Tour from './tour.vue'\nimport type { TourGap, TourMask } from './types'\n\nexport const tourProps = buildProps({\n  /**\n   * @description open tour\n   */\n  modelValue: Boolean,\n  /**\n   * @description what is the current step\n   */\n  current: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description whether to show the arrow\n   */\n  showArrow: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether to show a close button\n   */\n  showClose: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description custom close icon\n   */\n  closeIcon: {\n    type: iconPropType,\n  },\n  /**\n   * @description position of the guide card relative to the target element\n   */\n  placement: tourContentProps.placement,\n  /**\n   * @description custom style for content\n   */\n  contentStyle: {\n    type: definePropType<CSSProperties>([Object]),\n  },\n  /**\n   * @description whether to enable masking, change mask style and fill color by pass custom props\n   */\n  mask: {\n    type: definePropType<TourMask>([Boolean, Object]),\n    default: true,\n  },\n  /**\n   * @description transparent gap between mask and target\n   */\n  gap: {\n    type: definePropType<TourGap>(Object),\n    default: () => ({\n      offset: 6,\n      radius: 2,\n    }),\n  },\n  /**\n   * @description tour's zIndex\n   */\n  zIndex: {\n    type: Number,\n  },\n  /**\n   * @description support pass custom scrollIntoView options\n   */\n  scrollIntoViewOptions: {\n    type: definePropType<boolean | ScrollIntoViewOptions>([Boolean, Object]),\n    default: () => ({\n      block: 'center',\n    }),\n  },\n  /**\n   * @description type, affects the background color and text color\n   */\n  type: {\n    type: definePropType<'default' | 'primary'>(String),\n  },\n  /**\n   * @description which element the TourContent appends to\n   */\n  appendTo: {\n    type: teleportProps.to.type,\n    default: 'body',\n  },\n  /**\n   * @description whether the Tour can be closed by pressing ESC\n   */\n  closeOnPressEscape: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether the target element can be clickable, when using mask\n   */\n  targetAreaClickable: {\n    type: Boolean,\n    default: true,\n  },\n})\n\nexport type TourProps = ExtractPropTypes<typeof tourProps>\nexport type TourPropsPublic = __ExtractPublicPropTypes<typeof tourProps>\nexport type TourInstance = InstanceType<typeof Tour> & unknown\n\nexport const tourEmits = {\n  [UPDATE_MODEL_EVENT]: (value: boolean) => isBoolean(value),\n  ['update:current']: (current: number) => isNumber(current),\n  close: (current: number) => isNumber(current),\n  finish: () => true,\n  change: (current: number) => isNumber(current),\n}\nexport type TourEmits = typeof tourEmits\n"], "names": ["buildProps", "iconPropType", "tourContentProps", "definePropType", "teleportProps", "UPDATE_MODEL_EVENT", "isBoolean", "isNumber"], "mappings": ";;;;;;;;;;;AAUY,MAAC,SAAS,GAAGA,kBAAU,CAAC;AACpC,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEC,iBAAY;AACtB,GAAG;AACH,EAAE,SAAS,EAAEC,wBAAgB,CAAC,SAAS;AACvC,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,MAAM,CAAC,CAAC;AAClC,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAEA,sBAAc,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAC3C,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAEA,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO;AACpB,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,MAAM,EAAE,CAAC;AACf,KAAK,CAAC;AACN,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,qBAAqB,EAAE;AACzB,IAAI,IAAI,EAAEA,sBAAc,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAC3C,IAAI,OAAO,EAAE,OAAO;AACpB,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAEA,sBAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAEC,sBAAa,CAAC,EAAE,CAAC,IAAI;AAC/B,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,mBAAmB,EAAE;AACvB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,SAAS,GAAG;AACzB,EAAE,CAACC,wBAAkB,GAAG,CAAC,KAAK,KAAKC,eAAS,CAAC,KAAK,CAAC;AACnD,EAAE,CAAC,gBAAgB,GAAG,CAAC,OAAO,KAAKC,cAAQ,CAAC,OAAO,CAAC;AACpD,EAAE,KAAK,EAAE,CAAC,OAAO,KAAKA,cAAQ,CAAC,OAAO,CAAC;AACvC,EAAE,MAAM,EAAE,MAAM,IAAI;AACpB,EAAE,MAAM,EAAE,CAAC,OAAO,KAAKA,cAAQ,CAAC,OAAO,CAAC;AACxC;;;;;"}