<template>
  <div class="cached-layout-manager">
    <!-- 缓存的布局组件，避免重复渲染 -->
    <KeepAlive :include="cachedLayouts" :max="3">
      <component 
        :is="currentLayoutComponent" 
        :key="layoutKey"
        @layout-ready="onLayoutReady"
      />
    </KeepAlive>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import routeCache from '@/utils/RouteCache.js'
import AppLayout from './AppLayout.vue'
import BlankLayout from './BlankLayout.vue'

const route = useRoute()

// 缓存的布局组件列表
const cachedLayouts = ref(['AppLayout', 'BlankLayout'])

// 布局准备状态
const layoutReady = ref(false)

// 当前布局组件
const currentLayoutComponent = computed(() => {
  const layout = getCurrentLayout()
  console.log('CachedLayoutManager: 选择布局', {
    path: route.path,
    layout,
    fromCache: !!routeCache.getCachedLayout(route.path)
  })
  
  return layout === 'blank' ? BlankLayout : AppLayout
})

// 布局缓存键
const layoutKey = computed(() => {
  const layout = getCurrentLayout()
  return `${layout}-${route.path}`
})

/**
 * 获取当前路由的布局
 */
function getCurrentLayout() {
  // 1. 优先从缓存获取
  const cachedLayout = routeCache.getCachedLayout(route.path)
  if (cachedLayout) {
    console.log('CachedLayoutManager: 使用缓存布局', cachedLayout)
    return cachedLayout
  }
  
  // 2. 从路由 meta 获取
  const layout = route.meta?.layout || 'default'
  
  // 3. 缓存布局信息
  routeCache.cacheLayoutState(route.path, layout)
  
  return layout
}

/**
 * 布局组件准备完成
 */
function onLayoutReady() {
  layoutReady.value = true
  console.log('CachedLayoutManager: 布局准备完成')
}

/**
 * 预加载下一个可能的布局
 */
async function preloadNextLayout() {
  try {
    const authState = routeCache.getCachedAuthState()
    const nextRoute = routeCache.predictNextRoute(route.path, authState?.user?.role)
    
    if (nextRoute !== route.path) {
      console.log('CachedLayoutManager: 预加载下一个布局', nextRoute)
      
      // 预测下一个布局类型
      const nextLayout = nextRoute === '/login' ? 'blank' : 'default'
      routeCache.cacheLayoutState(nextRoute, nextLayout)
    }
  } catch (error) {
    console.warn('CachedLayoutManager: 预加载布局失败', error)
  }
}

/**
 * 监听路由变化，更新缓存
 */
watch(
  () => route.path,
  async (newPath, oldPath) => {
    console.log('CachedLayoutManager: 路由变化', { from: oldPath, to: newPath })
    
    // 缓存当前布局
    const layout = route.meta?.layout || 'default'
    routeCache.cacheLayoutState(newPath, layout)
    
    // 预加载下一个可能的布局
    await nextTick()
    preloadNextLayout()
  },
  { immediate: true }
)

/**
 * 组件挂载时的初始化
 */
onMounted(async () => {
  console.log('CachedLayoutManager: 组件挂载')
  
  // 预加载可能的布局
  await nextTick()
  preloadNextLayout()
  
  // 输出缓存统计
  const stats = routeCache.getCacheStats()
  console.log('CachedLayoutManager: 缓存统计', stats)
})

// 暴露给父组件的方法
defineExpose({
  layoutReady,
  getCurrentLayout,
  preloadNextLayout
})
</script>

<style scoped>
.cached-layout-manager {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* 布局切换动画 */
.layout-enter-active,
.layout-leave-active {
  transition: opacity 0.2s ease-in-out;
}

.layout-enter-from,
.layout-leave-to {
  opacity: 0;
}
</style>
