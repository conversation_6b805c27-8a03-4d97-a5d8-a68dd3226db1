{"version": 3, "file": "build-grid.js", "sources": ["../../../../../../../packages/components/virtual-list/src/builders/build-grid.ts"], "sourcesContent": ["import {\n  Fragment,\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  h,\n  nextTick,\n  onMounted,\n  ref,\n  resolveDynamicComponent,\n  unref,\n} from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport {\n  getScrollBarWidth,\n  hasOwn,\n  isClient,\n  isNumber,\n  isString,\n} from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport Scrollbar from '../components/scrollbar'\nimport { useGridWheel } from '../hooks/use-grid-wheel'\nimport { useCache } from '../hooks/use-cache'\nimport { virtualizedGridProps } from '../props'\nimport { getRTLOffsetType, getScrollDir, isRTL } from '../utils'\nimport {\n  AUTO_ALIGNMENT,\n  BACKWARD,\n  FORWARD,\n  ITEM_RENDER_EVT,\n  RTL,\n  RTL_OFFSET_NAG,\n  RTL_OFFSET_POS_ASC,\n  RTL_OFFSET_POS_DESC,\n  SCROLL_EVT,\n} from '../defaults'\n\nimport type {\n  CSSProperties,\n  Ref,\n  StyleValue,\n  UnwrapRef,\n  VNode,\n  VNodeChild,\n} from 'vue'\nimport type {\n  Alignment,\n  GridConstructorProps,\n  GridScrollOptions,\n  ScrollbarExpose,\n} from '../types'\nimport type { VirtualizedGridProps } from '../props'\nimport type { DynamicSizeGridInstance } from '../components/dynamic-size-grid.ts'\n\nconst createGrid = ({\n  name,\n  clearCache,\n  getColumnPosition,\n  getColumnStartIndexForOffset,\n  getColumnStopIndexForStartIndex,\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n  getColumnOffset,\n  getRowOffset,\n  getRowPosition,\n  getRowStartIndexForOffset,\n  getRowStopIndexForStartIndex,\n\n  initCache,\n  injectToInstance,\n  validateProps,\n}: GridConstructorProps<VirtualizedGridProps>) => {\n  return defineComponent({\n    name: name ?? 'ElVirtualList',\n    props: virtualizedGridProps,\n    emits: [ITEM_RENDER_EVT, SCROLL_EVT],\n    setup(props, { emit, expose, slots }) {\n      const ns = useNamespace('vl')\n\n      validateProps(props)\n      const instance = getCurrentInstance()!\n      const cache = ref(initCache(props, instance))\n      injectToInstance?.(instance, cache)\n      // refs\n      // here windowRef and innerRef can be type of HTMLElement\n      // or user defined component type, depends on the type passed\n      // by user\n      const windowRef = ref<HTMLElement>()\n      const hScrollbar = ref<ScrollbarExpose>()\n      const vScrollbar = ref<ScrollbarExpose>()\n      // innerRef is the actual container element which contains all the elements\n      const innerRef = ref(null)\n      const states = ref({\n        isScrolling: false,\n        scrollLeft: isNumber(props.initScrollLeft) ? props.initScrollLeft : 0,\n        scrollTop: isNumber(props.initScrollTop) ? props.initScrollTop : 0,\n        updateRequested: false,\n        xAxisScrollDir: FORWARD,\n        yAxisScrollDir: FORWARD,\n      })\n\n      const getItemStyleCache = useCache()\n\n      // computed\n      const parsedHeight = computed(() =>\n        Number.parseInt(`${props.height}`, 10)\n      )\n      const parsedWidth = computed(() => Number.parseInt(`${props.width}`, 10))\n      const columnsToRender = computed(() => {\n        const { totalColumn, totalRow, columnCache } = props\n        const { isScrolling, xAxisScrollDir, scrollLeft } = unref(states)\n\n        if (totalColumn === 0 || totalRow === 0) {\n          return [0, 0, 0, 0]\n        }\n\n        const startIndex = getColumnStartIndexForOffset(\n          props,\n          scrollLeft,\n          unref(cache)\n        )\n        const stopIndex = getColumnStopIndexForStartIndex(\n          props,\n          startIndex,\n          scrollLeft,\n          unref(cache)\n        )\n\n        const cacheBackward =\n          !isScrolling || xAxisScrollDir === BACKWARD\n            ? Math.max(1, columnCache)\n            : 1\n        const cacheForward =\n          !isScrolling || xAxisScrollDir === FORWARD\n            ? Math.max(1, columnCache)\n            : 1\n\n        return [\n          Math.max(0, startIndex - cacheBackward),\n          Math.max(0, Math.min(totalColumn! - 1, stopIndex + cacheForward)),\n          startIndex,\n          stopIndex,\n        ]\n      })\n\n      const rowsToRender = computed(() => {\n        const { totalColumn, totalRow, rowCache } = props\n        const { isScrolling, yAxisScrollDir, scrollTop } = unref(states)\n\n        if (totalColumn === 0 || totalRow === 0) {\n          return [0, 0, 0, 0]\n        }\n\n        const startIndex = getRowStartIndexForOffset(\n          props,\n          scrollTop,\n          unref(cache)\n        )\n        const stopIndex = getRowStopIndexForStartIndex(\n          props,\n          startIndex,\n          scrollTop,\n          unref(cache)\n        )\n\n        const cacheBackward =\n          !isScrolling || yAxisScrollDir === BACKWARD\n            ? Math.max(1, rowCache)\n            : 1\n        const cacheForward =\n          !isScrolling || yAxisScrollDir === FORWARD ? Math.max(1, rowCache) : 1\n\n        return [\n          Math.max(0, startIndex - cacheBackward),\n          Math.max(0, Math.min(totalRow! - 1, stopIndex + cacheForward)),\n          startIndex,\n          stopIndex,\n        ]\n      })\n\n      const estimatedTotalHeight = computed(() =>\n        getEstimatedTotalHeight(props, unref(cache))\n      )\n      const estimatedTotalWidth = computed(() =>\n        getEstimatedTotalWidth(props, unref(cache))\n      )\n\n      const windowStyle = computed<StyleValue>(() => [\n        {\n          position: 'relative',\n          overflow: 'hidden',\n          WebkitOverflowScrolling: 'touch',\n          willChange: 'transform',\n        },\n        {\n          direction: props.direction,\n          height: isNumber(props.height) ? `${props.height}px` : props.height,\n          width: isNumber(props.width) ? `${props.width}px` : props.width,\n        },\n        props.style ?? {},\n      ])\n\n      const innerStyle = computed(() => {\n        const width = `${unref(estimatedTotalWidth)}px`\n        const height = `${unref(estimatedTotalHeight)}px`\n\n        return {\n          height,\n          pointerEvents: unref(states).isScrolling ? 'none' : undefined,\n          width,\n        }\n      })\n\n      // methods\n      const emitEvents = () => {\n        const { totalColumn, totalRow } = props\n\n        if (totalColumn! > 0 && totalRow! > 0) {\n          const [\n            columnCacheStart,\n            columnCacheEnd,\n            columnVisibleStart,\n            columnVisibleEnd,\n          ] = unref(columnsToRender)\n          const [rowCacheStart, rowCacheEnd, rowVisibleStart, rowVisibleEnd] =\n            unref(rowsToRender)\n          // emit the render item event with\n          // [xAxisInvisibleStart, xAxisInvisibleEnd, xAxisVisibleStart, xAxisVisibleEnd]\n          // [yAxisInvisibleStart, yAxisInvisibleEnd, yAxisVisibleStart, yAxisVisibleEnd]\n          emit(ITEM_RENDER_EVT, {\n            columnCacheStart,\n            columnCacheEnd,\n            rowCacheStart,\n            rowCacheEnd,\n            columnVisibleStart,\n            columnVisibleEnd,\n            rowVisibleStart,\n            rowVisibleEnd,\n          })\n        }\n\n        const {\n          scrollLeft,\n          scrollTop,\n          updateRequested,\n          xAxisScrollDir,\n          yAxisScrollDir,\n        } = unref(states)\n        emit(SCROLL_EVT, {\n          xAxisScrollDir,\n          scrollLeft,\n          yAxisScrollDir,\n          scrollTop,\n          updateRequested,\n        })\n      }\n\n      const onScroll = (e: Event) => {\n        const {\n          clientHeight,\n          clientWidth,\n          scrollHeight,\n          scrollLeft,\n          scrollTop,\n          scrollWidth,\n        } = e.currentTarget as HTMLElement\n\n        const _states = unref(states)\n\n        if (\n          _states.scrollTop === scrollTop &&\n          _states.scrollLeft === scrollLeft\n        ) {\n          return\n        }\n\n        let _scrollLeft = scrollLeft\n\n        if (isRTL(props.direction)) {\n          switch (getRTLOffsetType()) {\n            case RTL_OFFSET_NAG:\n              _scrollLeft = -scrollLeft\n              break\n            case RTL_OFFSET_POS_DESC:\n              _scrollLeft = scrollWidth - clientWidth - scrollLeft\n              break\n          }\n        }\n\n        states.value = {\n          ..._states,\n          isScrolling: true,\n          scrollLeft: _scrollLeft,\n          scrollTop: Math.max(\n            0,\n            Math.min(scrollTop, scrollHeight - clientHeight)\n          ),\n          updateRequested: true,\n          xAxisScrollDir: getScrollDir(_states.scrollLeft, _scrollLeft),\n          yAxisScrollDir: getScrollDir(_states.scrollTop, scrollTop),\n        }\n\n        nextTick(() => resetIsScrolling())\n\n        onUpdated()\n        emitEvents()\n      }\n\n      const onVerticalScroll = (distance: number, totalSteps: number) => {\n        const height = unref(parsedHeight)\n        const offset =\n          ((estimatedTotalHeight.value - height) / totalSteps) * distance\n        scrollTo({\n          scrollTop: Math.min(estimatedTotalHeight.value - height, offset),\n        })\n      }\n\n      const onHorizontalScroll = (distance: number, totalSteps: number) => {\n        const width = unref(parsedWidth)\n        const offset =\n          ((estimatedTotalWidth.value - width) / totalSteps) * distance\n        scrollTo({\n          scrollLeft: Math.min(estimatedTotalWidth.value - width, offset),\n        })\n      }\n\n      const { onWheel } = useGridWheel(\n        {\n          atXStartEdge: computed(() => states.value.scrollLeft <= 0),\n          atXEndEdge: computed(\n            () =>\n              states.value.scrollLeft >=\n              estimatedTotalWidth.value - unref(parsedWidth)\n          ),\n          atYStartEdge: computed(() => states.value.scrollTop <= 0),\n          atYEndEdge: computed(\n            () =>\n              states.value.scrollTop >=\n              estimatedTotalHeight.value - unref(parsedHeight)\n          ),\n        },\n        (x: number, y: number) => {\n          hScrollbar.value?.onMouseUp?.()\n          vScrollbar.value?.onMouseUp?.()\n          const width = unref(parsedWidth)\n          const height = unref(parsedHeight)\n          scrollTo({\n            scrollLeft: Math.min(\n              states.value.scrollLeft + x,\n              estimatedTotalWidth.value - width\n            ),\n            scrollTop: Math.min(\n              states.value.scrollTop + y,\n              estimatedTotalHeight.value - height\n            ),\n          })\n        }\n      )\n\n      useEventListener(windowRef, 'wheel', onWheel, {\n        passive: false,\n      })\n\n      const scrollTo = ({\n        scrollLeft = states.value.scrollLeft,\n        scrollTop = states.value.scrollTop,\n      }: GridScrollOptions) => {\n        scrollLeft = Math.max(scrollLeft, 0)\n        scrollTop = Math.max(scrollTop, 0)\n        const _states = unref(states)\n        if (\n          scrollTop === _states.scrollTop &&\n          scrollLeft === _states.scrollLeft\n        ) {\n          return\n        }\n\n        states.value = {\n          ..._states,\n          xAxisScrollDir: getScrollDir(_states.scrollLeft, scrollLeft),\n          yAxisScrollDir: getScrollDir(_states.scrollTop, scrollTop),\n          scrollLeft,\n          scrollTop,\n          updateRequested: true,\n        }\n\n        nextTick(() => resetIsScrolling())\n\n        onUpdated()\n        emitEvents()\n      }\n\n      const scrollToItem = (\n        rowIndex = 0,\n        columnIdx = 0,\n        alignment: Alignment = AUTO_ALIGNMENT\n      ) => {\n        const _states = unref(states)\n        columnIdx = Math.max(0, Math.min(columnIdx, props.totalColumn! - 1))\n        rowIndex = Math.max(0, Math.min(rowIndex, props.totalRow! - 1))\n        const scrollBarWidth = getScrollBarWidth(ns.namespace.value)\n\n        const _cache = unref(cache)\n        const estimatedHeight = getEstimatedTotalHeight(props, _cache)\n        const estimatedWidth = getEstimatedTotalWidth(props, _cache)\n\n        scrollTo({\n          scrollLeft: getColumnOffset(\n            props,\n            columnIdx,\n            alignment,\n            _states.scrollLeft,\n            _cache,\n            estimatedWidth > (props.width as number) ? scrollBarWidth : 0\n          ),\n          scrollTop: getRowOffset(\n            props,\n            rowIndex,\n            alignment,\n            _states.scrollTop,\n            _cache,\n            estimatedHeight > (props.height as number) ? scrollBarWidth : 0\n          ),\n        })\n      }\n\n      const getItemStyle = (rowIndex: number, columnIndex: number) => {\n        const { columnWidth, direction, rowHeight } = props\n        const itemStyleCache = getItemStyleCache.value(\n          clearCache && columnWidth,\n          clearCache && rowHeight,\n          clearCache && direction\n        )\n        // since there was no need to introduce an nested array into cache object\n        // we use row,column to construct the key for indexing the map.\n        const key = `${rowIndex},${columnIndex}`\n\n        if (hasOwn(itemStyleCache, key)) {\n          return itemStyleCache[key] as CSSProperties\n        } else {\n          const [, left] = getColumnPosition(props, columnIndex, unref(cache))\n          const _cache = unref(cache)\n\n          const rtl = isRTL(direction)\n          const [height, top] = getRowPosition(props, rowIndex, _cache)\n          const [width] = getColumnPosition(props, columnIndex, _cache)\n\n          itemStyleCache[key] = {\n            position: 'absolute',\n            left: rtl ? undefined : `${left}px`,\n            right: rtl ? `${left}px` : undefined,\n            top: `${top}px`,\n            height: `${height}px`,\n            width: `${width}px`,\n          }\n\n          return itemStyleCache[key] as CSSProperties\n        }\n      }\n\n      // TODO: debounce setting is scrolling.\n\n      const resetIsScrolling = () => {\n        states.value.isScrolling = false\n        nextTick(() => {\n          getItemStyleCache.value(-1, null, null)\n        })\n      }\n\n      // life cycles\n      onMounted(() => {\n        // for SSR\n        if (!isClient) return\n        const { initScrollLeft, initScrollTop } = props\n        const windowElement = unref(windowRef)\n        if (windowElement) {\n          if (isNumber(initScrollLeft)) {\n            windowElement.scrollLeft = initScrollLeft\n          }\n          if (isNumber(initScrollTop)) {\n            windowElement.scrollTop = initScrollTop\n          }\n        }\n        emitEvents()\n      })\n\n      const onUpdated = () => {\n        const { direction } = props\n        const { scrollLeft, scrollTop, updateRequested } = unref(states)\n\n        const windowElement = unref(windowRef)\n        if (updateRequested && windowElement) {\n          if (direction === RTL) {\n            switch (getRTLOffsetType()) {\n              case RTL_OFFSET_NAG: {\n                windowElement.scrollLeft = -scrollLeft\n                break\n              }\n              case RTL_OFFSET_POS_ASC: {\n                windowElement.scrollLeft = scrollLeft\n                break\n              }\n              default: {\n                const { clientWidth, scrollWidth } = windowElement\n                windowElement.scrollLeft =\n                  scrollWidth - clientWidth - scrollLeft\n                break\n              }\n            }\n          } else {\n            windowElement.scrollLeft = Math.max(0, scrollLeft)\n          }\n\n          windowElement.scrollTop = Math.max(0, scrollTop)\n        }\n      }\n\n      const { resetAfterColumnIndex, resetAfterRowIndex, resetAfter } =\n        instance.proxy as DynamicSizeGridInstance\n\n      expose({\n        windowRef,\n        innerRef,\n        getItemStyleCache,\n        scrollTo,\n        scrollToItem,\n        states,\n        resetAfterColumnIndex,\n        resetAfterRowIndex,\n        resetAfter,\n      })\n\n      // rendering part\n\n      const renderScrollbars = () => {\n        const {\n          scrollbarAlwaysOn,\n          scrollbarStartGap,\n          scrollbarEndGap,\n          totalColumn,\n          totalRow,\n        } = props\n\n        const width = unref(parsedWidth)\n        const height = unref(parsedHeight)\n        const estimatedWidth = unref(estimatedTotalWidth)\n        const estimatedHeight = unref(estimatedTotalHeight)\n        const { scrollLeft, scrollTop } = unref(states)\n        const horizontalScrollbar = h(Scrollbar, {\n          ref: hScrollbar,\n          alwaysOn: scrollbarAlwaysOn,\n          startGap: scrollbarStartGap,\n          endGap: scrollbarEndGap,\n          class: ns.e('horizontal'),\n          clientSize: width,\n          layout: 'horizontal',\n          onScroll: onHorizontalScroll,\n          ratio: (width * 100) / estimatedWidth,\n          scrollFrom: scrollLeft / (estimatedWidth - width),\n          total: totalRow,\n          visible: true,\n        })\n\n        const verticalScrollbar = h(Scrollbar, {\n          ref: vScrollbar,\n          alwaysOn: scrollbarAlwaysOn,\n          startGap: scrollbarStartGap,\n          endGap: scrollbarEndGap,\n          class: ns.e('vertical'),\n          clientSize: height,\n          layout: 'vertical',\n          onScroll: onVerticalScroll,\n          ratio: (height * 100) / estimatedHeight,\n          scrollFrom: scrollTop / (estimatedHeight - height),\n\n          total: totalColumn,\n          visible: true,\n        })\n\n        return {\n          horizontalScrollbar,\n          verticalScrollbar,\n        }\n      }\n\n      const renderItems = () => {\n        const [columnStart, columnEnd] = unref(columnsToRender)\n        const [rowStart, rowEnd] = unref(rowsToRender)\n        const { data, totalColumn, totalRow, useIsScrolling, itemKey } = props\n        const children: VNodeChild[] = []\n        if (totalRow > 0 && totalColumn > 0) {\n          for (let row = rowStart; row <= rowEnd; row++) {\n            for (let column = columnStart; column <= columnEnd; column++) {\n              const key = itemKey({ columnIndex: column, data, rowIndex: row })\n              children.push(\n                h(\n                  Fragment,\n                  { key },\n                  slots.default?.({\n                    columnIndex: column,\n                    data,\n                    isScrolling: useIsScrolling\n                      ? unref(states).isScrolling\n                      : undefined,\n                    style: getItemStyle(row, column),\n                    rowIndex: row,\n                  })\n                )\n              )\n            }\n          }\n        }\n        return children\n      }\n\n      const renderInner = () => {\n        const Inner = resolveDynamicComponent(props.innerElement) as VNode\n        const children = renderItems()\n        return [\n          h(\n            Inner,\n            {\n              style: unref(innerStyle),\n              ref: innerRef,\n            },\n            !isString(Inner)\n              ? {\n                  default: () => children,\n                }\n              : children\n          ),\n        ]\n      }\n\n      const renderWindow = () => {\n        const Container = resolveDynamicComponent(\n          props.containerElement\n        ) as VNode\n        const { horizontalScrollbar, verticalScrollbar } = renderScrollbars()\n        const Inner = renderInner()\n\n        return h(\n          'div',\n          {\n            key: 0,\n            class: ns.e('wrapper'),\n            role: props.role,\n          },\n          [\n            h(\n              Container,\n              {\n                class: props.className,\n                style: unref(windowStyle),\n                onScroll,\n                ref: windowRef,\n              },\n              !isString(Container) ? { default: () => Inner } : Inner\n            ),\n            horizontalScrollbar,\n            verticalScrollbar,\n          ]\n        )\n      }\n\n      return renderWindow\n    },\n  })\n}\n\nexport default createGrid\n\ntype Dir = typeof FORWARD | typeof BACKWARD\n\nexport type GridInstance = InstanceType<ReturnType<typeof createGrid>> &\n  UnwrapRef<{\n    windowRef: Ref<HTMLElement>\n    innerRef: Ref<HTMLElement>\n    getItemStyleCache: ReturnType<typeof useCache>\n    scrollTo: (scrollOptions: GridScrollOptions) => void\n    scrollToItem: (\n      rowIndex: number,\n      columnIndex: number,\n      alignment: Alignment\n    ) => void\n    states: Ref<{\n      isScrolling: boolean\n      scrollLeft: number\n      scrollTop: number\n      updateRequested: boolean\n      xAxisScrollDir: Dir\n      yAxisScrollDir: Dir\n    }>\n  }>\n"], "names": ["defineComponent", "virtualizedGridProps", "ITEM_RENDER_EVT", "SCROLL_EVT", "useNamespace", "getCurrentInstance", "ref", "isNumber", "FORWARD", "useCache", "computed", "unref", "BACKWARD", "isRTL", "getRTLOffsetType", "RTL_OFFSET_NAG", "RTL_OFFSET_POS_DESC", "getScrollDir", "nextTick", "useGridWheel", "useEventListener", "AUTO_ALIGNMENT", "getScrollBarWidth", "hasOwn", "onMounted", "isClient", "RTL", "RTL_OFFSET_POS_ASC", "h", "Sc<PERSON><PERSON>", "Fragment", "resolveDynamicComponent", "isString"], "mappings": ";;;;;;;;;;;;;;;;;AAqCK,MAAC,UAAU,GAAG,CAAC;AACpB,EAAE,IAAI;AACN,EAAE,UAAU;AACZ,EAAE,iBAAiB;AACnB,EAAE,4BAA4B;AAC9B,EAAE,+BAA+B;AACjC,EAAE,uBAAuB;AACzB,EAAE,sBAAsB;AACxB,EAAE,eAAe;AACjB,EAAE,YAAY;AACd,EAAE,cAAc;AAChB,EAAE,yBAAyB;AAC3B,EAAE,4BAA4B;AAC9B,EAAE,SAAS;AACX,EAAE,gBAAgB;AAClB,EAAE,aAAa;AACf,CAAC,KAAK;AACN,EAAE,OAAOA,mBAAe,CAAC;AACzB,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,eAAe;AAC/C,IAAI,KAAK,EAAEC,0BAAoB;AAC/B,IAAI,KAAK,EAAE,CAACC,wBAAe,EAAEC,mBAAU,CAAC;AACxC,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE;AAC1C,MAAM,MAAM,EAAE,GAAGC,kBAAY,CAAC,IAAI,CAAC,CAAC;AACpC,MAAM,aAAa,CAAC,KAAK,CAAC,CAAC;AAC3B,MAAM,MAAM,QAAQ,GAAGC,sBAAkB,EAAE,CAAC;AAC5C,MAAM,MAAM,KAAK,GAAGC,OAAG,CAAC,SAAS,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;AACpD,MAAM,gBAAgB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC5E,MAAM,MAAM,SAAS,GAAGA,OAAG,EAAE,CAAC;AAC9B,MAAM,MAAM,UAAU,GAAGA,OAAG,EAAE,CAAC;AAC/B,MAAM,MAAM,UAAU,GAAGA,OAAG,EAAE,CAAC;AAC/B,MAAM,MAAM,QAAQ,GAAGA,OAAG,CAAC,IAAI,CAAC,CAAC;AACjC,MAAM,MAAM,MAAM,GAAGA,OAAG,CAAC;AACzB,QAAQ,WAAW,EAAE,KAAK;AAC1B,QAAQ,UAAU,EAAEC,cAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,KAAK,CAAC,cAAc,GAAG,CAAC;AAC7E,QAAQ,SAAS,EAAEA,cAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC,aAAa,GAAG,CAAC;AAC1E,QAAQ,eAAe,EAAE,KAAK;AAC9B,QAAQ,cAAc,EAAEC,gBAAO;AAC/B,QAAQ,cAAc,EAAEA,gBAAO;AAC/B,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,iBAAiB,GAAGC,iBAAQ,EAAE,CAAC;AAC3C,MAAM,MAAM,YAAY,GAAGC,YAAQ,CAAC,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAClF,MAAM,MAAM,WAAW,GAAGA,YAAQ,CAAC,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAChF,MAAM,MAAM,eAAe,GAAGA,YAAQ,CAAC,MAAM;AAC7C,QAAQ,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;AAC7D,QAAQ,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,UAAU,EAAE,GAAGC,SAAK,CAAC,MAAM,CAAC,CAAC;AAC1E,QAAQ,IAAI,WAAW,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,EAAE;AACjD,UAAU,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9B,SAAS;AACT,QAAQ,MAAM,UAAU,GAAG,4BAA4B,CAAC,KAAK,EAAE,UAAU,EAAEA,SAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACzF,QAAQ,MAAM,SAAS,GAAG,+BAA+B,CAAC,KAAK,EAAE,UAAU,EAAE,UAAU,EAAEA,SAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACvG,QAAQ,MAAM,aAAa,GAAG,CAAC,WAAW,IAAI,cAAc,KAAKC,iBAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;AACzG,QAAQ,MAAM,YAAY,GAAG,CAAC,WAAW,IAAI,cAAc,KAAKJ,gBAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;AACvG,QAAQ,OAAO;AACf,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,GAAG,aAAa,CAAC;AACjD,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,CAAC;AAC1E,UAAU,UAAU;AACpB,UAAU,SAAS;AACnB,SAAS,CAAC;AACV,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,YAAY,GAAGE,YAAQ,CAAC,MAAM;AAC1C,QAAQ,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC;AAC1D,QAAQ,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,SAAS,EAAE,GAAGC,SAAK,CAAC,MAAM,CAAC,CAAC;AACzE,QAAQ,IAAI,WAAW,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,EAAE;AACjD,UAAU,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9B,SAAS;AACT,QAAQ,MAAM,UAAU,GAAG,yBAAyB,CAAC,KAAK,EAAE,SAAS,EAAEA,SAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACrF,QAAQ,MAAM,SAAS,GAAG,4BAA4B,CAAC,KAAK,EAAE,UAAU,EAAE,SAAS,EAAEA,SAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACnG,QAAQ,MAAM,aAAa,GAAG,CAAC,WAAW,IAAI,cAAc,KAAKC,iBAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;AACtG,QAAQ,MAAM,YAAY,GAAG,CAAC,WAAW,IAAI,cAAc,KAAKJ,gBAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;AACpG,QAAQ,OAAO;AACf,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,GAAG,aAAa,CAAC;AACjD,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,CAAC;AACvE,UAAU,UAAU;AACpB,UAAU,SAAS;AACnB,SAAS,CAAC;AACV,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,oBAAoB,GAAGE,YAAQ,CAAC,MAAM,uBAAuB,CAAC,KAAK,EAAEC,SAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChG,MAAM,MAAM,mBAAmB,GAAGD,YAAQ,CAAC,MAAM,sBAAsB,CAAC,KAAK,EAAEC,SAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC9F,MAAM,MAAM,WAAW,GAAGD,YAAQ,CAAC,MAAM;AACzC,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,OAAO;AACf,UAAU;AACV,YAAY,QAAQ,EAAE,UAAU;AAChC,YAAY,QAAQ,EAAE,QAAQ;AAC9B,YAAY,uBAAuB,EAAE,OAAO;AAC5C,YAAY,UAAU,EAAE,WAAW;AACnC,WAAW;AACX,UAAU;AACV,YAAY,SAAS,EAAE,KAAK,CAAC,SAAS;AACtC,YAAY,MAAM,EAAEH,cAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM;AAC/E,YAAY,KAAK,EAAEA,cAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,KAAK;AAC3E,WAAW;AACX,UAAU,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE;AAC9C,SAAS,CAAC;AACV,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,UAAU,GAAGG,YAAQ,CAAC,MAAM;AACxC,QAAQ,MAAM,KAAK,GAAG,CAAC,EAAEC,SAAK,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,CAAC;AACxD,QAAQ,MAAM,MAAM,GAAG,CAAC,EAAEA,SAAK,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1D,QAAQ,OAAO;AACf,UAAU,MAAM;AAChB,UAAU,aAAa,EAAEA,SAAK,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,MAAM,GAAG,KAAK,CAAC;AACpE,UAAU,KAAK;AACf,SAAS,CAAC;AACV,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,UAAU,GAAG,MAAM;AAC/B,QAAQ,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC;AAChD,QAAQ,IAAI,WAAW,GAAG,CAAC,IAAI,QAAQ,GAAG,CAAC,EAAE;AAC7C,UAAU,MAAM;AAChB,YAAY,gBAAgB;AAC5B,YAAY,cAAc;AAC1B,YAAY,kBAAkB;AAC9B,YAAY,gBAAgB;AAC5B,WAAW,GAAGA,SAAK,CAAC,eAAe,CAAC,CAAC;AACrC,UAAU,MAAM,CAAC,aAAa,EAAE,WAAW,EAAE,eAAe,EAAE,aAAa,CAAC,GAAGA,SAAK,CAAC,YAAY,CAAC,CAAC;AACnG,UAAU,IAAI,CAACT,wBAAe,EAAE;AAChC,YAAY,gBAAgB;AAC5B,YAAY,cAAc;AAC1B,YAAY,aAAa;AACzB,YAAY,WAAW;AACvB,YAAY,kBAAkB;AAC9B,YAAY,gBAAgB;AAC5B,YAAY,eAAe;AAC3B,YAAY,aAAa;AACzB,WAAW,CAAC,CAAC;AACb,SAAS;AACT,QAAQ,MAAM;AACd,UAAU,UAAU;AACpB,UAAU,SAAS;AACnB,UAAU,eAAe;AACzB,UAAU,cAAc;AACxB,UAAU,cAAc;AACxB,SAAS,GAAGS,SAAK,CAAC,MAAM,CAAC,CAAC;AAC1B,QAAQ,IAAI,CAACR,mBAAU,EAAE;AACzB,UAAU,cAAc;AACxB,UAAU,UAAU;AACpB,UAAU,cAAc;AACxB,UAAU,SAAS;AACnB,UAAU,eAAe;AACzB,SAAS,CAAC,CAAC;AACX,OAAO,CAAC;AACR,MAAM,MAAM,QAAQ,GAAG,CAAC,CAAC,KAAK;AAC9B,QAAQ,MAAM;AACd,UAAU,YAAY;AACtB,UAAU,WAAW;AACrB,UAAU,YAAY;AACtB,UAAU,UAAU;AACpB,UAAU,SAAS;AACnB,UAAU,WAAW;AACrB,SAAS,GAAG,CAAC,CAAC,aAAa,CAAC;AAC5B,QAAQ,MAAM,OAAO,GAAGQ,SAAK,CAAC,MAAM,CAAC,CAAC;AACtC,QAAQ,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,IAAI,OAAO,CAAC,UAAU,KAAK,UAAU,EAAE;AAClF,UAAU,OAAO;AACjB,SAAS;AACT,QAAQ,IAAI,WAAW,GAAG,UAAU,CAAC;AACrC,QAAQ,IAAIE,WAAK,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;AACpC,UAAU,QAAQC,sBAAgB,EAAE;AACpC,YAAY,KAAKC,uBAAc;AAC/B,cAAc,WAAW,GAAG,CAAC,UAAU,CAAC;AACxC,cAAc,MAAM;AACpB,YAAY,KAAKC,4BAAmB;AACpC,cAAc,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,UAAU,CAAC;AACnE,cAAc,MAAM;AACpB,WAAW;AACX,SAAS;AACT,QAAQ,MAAM,CAAC,KAAK,GAAG;AACvB,UAAU,GAAG,OAAO;AACpB,UAAU,WAAW,EAAE,IAAI;AAC3B,UAAU,UAAU,EAAE,WAAW;AACjC,UAAU,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,GAAG,YAAY,CAAC,CAAC;AAClF,UAAU,eAAe,EAAE,IAAI;AAC/B,UAAU,cAAc,EAAEC,kBAAY,CAAC,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC;AACvE,UAAU,cAAc,EAAEA,kBAAY,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC;AACpE,SAAS,CAAC;AACV,QAAQC,YAAQ,CAAC,MAAM,gBAAgB,EAAE,CAAC,CAAC;AAC3C,QAAQ,SAAS,EAAE,CAAC;AACpB,QAAQ,UAAU,EAAE,CAAC;AACrB,OAAO,CAAC;AACR,MAAM,MAAM,gBAAgB,GAAG,CAAC,QAAQ,EAAE,UAAU,KAAK;AACzD,QAAQ,MAAM,MAAM,GAAGP,SAAK,CAAC,YAAY,CAAC,CAAC;AAC3C,QAAQ,MAAM,MAAM,GAAG,CAAC,oBAAoB,CAAC,KAAK,GAAG,MAAM,IAAI,UAAU,GAAG,QAAQ,CAAC;AACrF,QAAQ,QAAQ,CAAC;AACjB,UAAU,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,KAAK,GAAG,MAAM,EAAE,MAAM,CAAC;AAC1E,SAAS,CAAC,CAAC;AACX,OAAO,CAAC;AACR,MAAM,MAAM,kBAAkB,GAAG,CAAC,QAAQ,EAAE,UAAU,KAAK;AAC3D,QAAQ,MAAM,KAAK,GAAGA,SAAK,CAAC,WAAW,CAAC,CAAC;AACzC,QAAQ,MAAM,MAAM,GAAG,CAAC,mBAAmB,CAAC,KAAK,GAAG,KAAK,IAAI,UAAU,GAAG,QAAQ,CAAC;AACnF,QAAQ,QAAQ,CAAC;AACjB,UAAU,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,KAAK,GAAG,KAAK,EAAE,MAAM,CAAC;AACzE,SAAS,CAAC,CAAC;AACX,OAAO,CAAC;AACR,MAAM,MAAM,EAAE,OAAO,EAAE,GAAGQ,yBAAY,CAAC;AACvC,QAAQ,YAAY,EAAET,YAAQ,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC;AAClE,QAAQ,UAAU,EAAEA,YAAQ,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,UAAU,IAAI,mBAAmB,CAAC,KAAK,GAAGC,SAAK,CAAC,WAAW,CAAC,CAAC;AAC7G,QAAQ,YAAY,EAAED,YAAQ,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC;AACjE,QAAQ,UAAU,EAAEA,YAAQ,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,SAAS,IAAI,oBAAoB,CAAC,KAAK,GAAGC,SAAK,CAAC,YAAY,CAAC,CAAC;AAC9G,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK;AACnB,QAAQ,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC3B,QAAQ,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtG,QAAQ,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtG,QAAQ,MAAM,KAAK,GAAGA,SAAK,CAAC,WAAW,CAAC,CAAC;AACzC,QAAQ,MAAM,MAAM,GAAGA,SAAK,CAAC,YAAY,CAAC,CAAC;AAC3C,QAAQ,QAAQ,CAAC;AACjB,UAAU,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,EAAE,mBAAmB,CAAC,KAAK,GAAG,KAAK,CAAC;AAC9F,UAAU,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,oBAAoB,CAAC,KAAK,GAAG,MAAM,CAAC;AAC9F,SAAS,CAAC,CAAC;AACX,OAAO,CAAC,CAAC;AACT,MAAMS,qBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE;AACpD,QAAQ,OAAO,EAAE,KAAK;AACtB,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,QAAQ,GAAG,CAAC;AACxB,QAAQ,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU;AAC5C,QAAQ,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS;AAC1C,OAAO,KAAK;AACZ,QAAQ,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AAC7C,QAAQ,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AAC3C,QAAQ,MAAM,OAAO,GAAGT,SAAK,CAAC,MAAM,CAAC,CAAC;AACtC,QAAQ,IAAI,SAAS,KAAK,OAAO,CAAC,SAAS,IAAI,UAAU,KAAK,OAAO,CAAC,UAAU,EAAE;AAClF,UAAU,OAAO;AACjB,SAAS;AACT,QAAQ,MAAM,CAAC,KAAK,GAAG;AACvB,UAAU,GAAG,OAAO;AACpB,UAAU,cAAc,EAAEM,kBAAY,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC;AACtE,UAAU,cAAc,EAAEA,kBAAY,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC;AACpE,UAAU,UAAU;AACpB,UAAU,SAAS;AACnB,UAAU,eAAe,EAAE,IAAI;AAC/B,SAAS,CAAC;AACV,QAAQC,YAAQ,CAAC,MAAM,gBAAgB,EAAE,CAAC,CAAC;AAC3C,QAAQ,SAAS,EAAE,CAAC;AACpB,QAAQ,UAAU,EAAE,CAAC;AACrB,OAAO,CAAC;AACR,MAAM,MAAM,YAAY,GAAG,CAAC,QAAQ,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,SAAS,GAAGG,uBAAc,KAAK;AACxF,QAAQ,MAAM,OAAO,GAAGV,SAAK,CAAC,MAAM,CAAC,CAAC;AACtC,QAAQ,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AAC5E,QAAQ,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;AACvE,QAAQ,MAAM,cAAc,GAAGW,wBAAiB,CAAC,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACrE,QAAQ,MAAM,MAAM,GAAGX,SAAK,CAAC,KAAK,CAAC,CAAC;AACpC,QAAQ,MAAM,eAAe,GAAG,uBAAuB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACvE,QAAQ,MAAM,cAAc,GAAG,sBAAsB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACrE,QAAQ,QAAQ,CAAC;AACjB,UAAU,UAAU,EAAE,eAAe,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE,cAAc,GAAG,KAAK,CAAC,KAAK,GAAG,cAAc,GAAG,CAAC,CAAC;AACjJ,UAAU,SAAS,EAAE,YAAY,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,MAAM,EAAE,eAAe,GAAG,KAAK,CAAC,MAAM,GAAG,cAAc,GAAG,CAAC,CAAC;AAC7I,SAAS,CAAC,CAAC;AACX,OAAO,CAAC;AACR,MAAM,MAAM,YAAY,GAAG,CAAC,QAAQ,EAAE,WAAW,KAAK;AACtD,QAAQ,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;AAC5D,QAAQ,MAAM,cAAc,GAAG,iBAAiB,CAAC,KAAK,CAAC,UAAU,IAAI,WAAW,EAAE,UAAU,IAAI,SAAS,EAAE,UAAU,IAAI,SAAS,CAAC,CAAC;AACpI,QAAQ,MAAM,GAAG,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;AACjD,QAAQ,IAAIY,aAAM,CAAC,cAAc,EAAE,GAAG,CAAC,EAAE;AACzC,UAAU,OAAO,cAAc,CAAC,GAAG,CAAC,CAAC;AACrC,SAAS,MAAM;AACf,UAAU,MAAM,GAAG,IAAI,CAAC,GAAG,iBAAiB,CAAC,KAAK,EAAE,WAAW,EAAEZ,SAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/E,UAAU,MAAM,MAAM,GAAGA,SAAK,CAAC,KAAK,CAAC,CAAC;AACtC,UAAU,MAAM,GAAG,GAAGE,WAAK,CAAC,SAAS,CAAC,CAAC;AACvC,UAAU,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;AACxE,UAAU,MAAM,CAAC,KAAK,CAAC,GAAG,iBAAiB,CAAC,KAAK,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;AACxE,UAAU,cAAc,CAAC,GAAG,CAAC,GAAG;AAChC,YAAY,QAAQ,EAAE,UAAU;AAChC,YAAY,IAAI,EAAE,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC;AAC5C,YAAY,KAAK,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;AAC7C,YAAY,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;AAC3B,YAAY,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC;AACjC,YAAY,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC;AAC/B,WAAW,CAAC;AACZ,UAAU,OAAO,cAAc,CAAC,GAAG,CAAC,CAAC;AACrC,SAAS;AACT,OAAO,CAAC;AACR,MAAM,MAAM,gBAAgB,GAAG,MAAM;AACrC,QAAQ,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;AACzC,QAAQK,YAAQ,CAAC,MAAM;AACvB,UAAU,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAClD,SAAS,CAAC,CAAC;AACX,OAAO,CAAC;AACR,MAAMM,aAAS,CAAC,MAAM;AACtB,QAAQ,IAAI,CAACC,aAAQ;AACrB,UAAU,OAAO;AACjB,QAAQ,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AACxD,QAAQ,MAAM,aAAa,GAAGd,SAAK,CAAC,SAAS,CAAC,CAAC;AAC/C,QAAQ,IAAI,aAAa,EAAE;AAC3B,UAAU,IAAIJ,cAAQ,CAAC,cAAc,CAAC,EAAE;AACxC,YAAY,aAAa,CAAC,UAAU,GAAG,cAAc,CAAC;AACtD,WAAW;AACX,UAAU,IAAIA,cAAQ,CAAC,aAAa,CAAC,EAAE;AACvC,YAAY,aAAa,CAAC,SAAS,GAAG,aAAa,CAAC;AACpD,WAAW;AACX,SAAS;AACT,QAAQ,UAAU,EAAE,CAAC;AACrB,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,SAAS,GAAG,MAAM;AAC9B,QAAQ,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;AACpC,QAAQ,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,eAAe,EAAE,GAAGI,SAAK,CAAC,MAAM,CAAC,CAAC;AACzE,QAAQ,MAAM,aAAa,GAAGA,SAAK,CAAC,SAAS,CAAC,CAAC;AAC/C,QAAQ,IAAI,eAAe,IAAI,aAAa,EAAE;AAC9C,UAAU,IAAI,SAAS,KAAKe,YAAG,EAAE;AACjC,YAAY,QAAQZ,sBAAgB,EAAE;AACtC,cAAc,KAAKC,uBAAc,EAAE;AACnC,gBAAgB,aAAa,CAAC,UAAU,GAAG,CAAC,UAAU,CAAC;AACvD,gBAAgB,MAAM;AACtB,eAAe;AACf,cAAc,KAAKY,2BAAkB,EAAE;AACvC,gBAAgB,aAAa,CAAC,UAAU,GAAG,UAAU,CAAC;AACtD,gBAAgB,MAAM;AACtB,eAAe;AACf,cAAc,SAAS;AACvB,gBAAgB,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,aAAa,CAAC;AACnE,gBAAgB,aAAa,CAAC,UAAU,GAAG,WAAW,GAAG,WAAW,GAAG,UAAU,CAAC;AAClF,gBAAgB,MAAM;AACtB,eAAe;AACf,aAAa;AACb,WAAW,MAAM;AACjB,YAAY,aAAa,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAC/D,WAAW;AACX,UAAU,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AAC3D,SAAS;AACT,OAAO,CAAC;AACR,MAAM,MAAM,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,UAAU,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC;AACvF,MAAM,MAAM,CAAC;AACb,QAAQ,SAAS;AACjB,QAAQ,QAAQ;AAChB,QAAQ,iBAAiB;AACzB,QAAQ,QAAQ;AAChB,QAAQ,YAAY;AACpB,QAAQ,MAAM;AACd,QAAQ,qBAAqB;AAC7B,QAAQ,kBAAkB;AAC1B,QAAQ,UAAU;AAClB,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,gBAAgB,GAAG,MAAM;AACrC,QAAQ,MAAM;AACd,UAAU,iBAAiB;AAC3B,UAAU,iBAAiB;AAC3B,UAAU,eAAe;AACzB,UAAU,WAAW;AACrB,UAAU,QAAQ;AAClB,SAAS,GAAG,KAAK,CAAC;AAClB,QAAQ,MAAM,KAAK,GAAGhB,SAAK,CAAC,WAAW,CAAC,CAAC;AACzC,QAAQ,MAAM,MAAM,GAAGA,SAAK,CAAC,YAAY,CAAC,CAAC;AAC3C,QAAQ,MAAM,cAAc,GAAGA,SAAK,CAAC,mBAAmB,CAAC,CAAC;AAC1D,QAAQ,MAAM,eAAe,GAAGA,SAAK,CAAC,oBAAoB,CAAC,CAAC;AAC5D,QAAQ,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,GAAGA,SAAK,CAAC,MAAM,CAAC,CAAC;AACxD,QAAQ,MAAM,mBAAmB,GAAGiB,KAAC,CAACC,oBAAS,EAAE;AACjD,UAAU,GAAG,EAAE,UAAU;AACzB,UAAU,QAAQ,EAAE,iBAAiB;AACrC,UAAU,QAAQ,EAAE,iBAAiB;AACrC,UAAU,MAAM,EAAE,eAAe;AACjC,UAAU,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC;AACnC,UAAU,UAAU,EAAE,KAAK;AAC3B,UAAU,MAAM,EAAE,YAAY;AAC9B,UAAU,QAAQ,EAAE,kBAAkB;AACtC,UAAU,KAAK,EAAE,KAAK,GAAG,GAAG,GAAG,cAAc;AAC7C,UAAU,UAAU,EAAE,UAAU,IAAI,cAAc,GAAG,KAAK,CAAC;AAC3D,UAAU,KAAK,EAAE,QAAQ;AACzB,UAAU,OAAO,EAAE,IAAI;AACvB,SAAS,CAAC,CAAC;AACX,QAAQ,MAAM,iBAAiB,GAAGD,KAAC,CAACC,oBAAS,EAAE;AAC/C,UAAU,GAAG,EAAE,UAAU;AACzB,UAAU,QAAQ,EAAE,iBAAiB;AACrC,UAAU,QAAQ,EAAE,iBAAiB;AACrC,UAAU,MAAM,EAAE,eAAe;AACjC,UAAU,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC;AACjC,UAAU,UAAU,EAAE,MAAM;AAC5B,UAAU,MAAM,EAAE,UAAU;AAC5B,UAAU,QAAQ,EAAE,gBAAgB;AACpC,UAAU,KAAK,EAAE,MAAM,GAAG,GAAG,GAAG,eAAe;AAC/C,UAAU,UAAU,EAAE,SAAS,IAAI,eAAe,GAAG,MAAM,CAAC;AAC5D,UAAU,KAAK,EAAE,WAAW;AAC5B,UAAU,OAAO,EAAE,IAAI;AACvB,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO;AACf,UAAU,mBAAmB;AAC7B,UAAU,iBAAiB;AAC3B,SAAS,CAAC;AACV,OAAO,CAAC;AACR,MAAM,MAAM,WAAW,GAAG,MAAM;AAChC,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,MAAM,CAAC,WAAW,EAAE,SAAS,CAAC,GAAGlB,SAAK,CAAC,eAAe,CAAC,CAAC;AAChE,QAAQ,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,GAAGA,SAAK,CAAC,YAAY,CAAC,CAAC;AACvD,QAAQ,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;AAC/E,QAAQ,MAAM,QAAQ,GAAG,EAAE,CAAC;AAC5B,QAAQ,IAAI,QAAQ,GAAG,CAAC,IAAI,WAAW,GAAG,CAAC,EAAE;AAC7C,UAAU,KAAK,IAAI,GAAG,GAAG,QAAQ,EAAE,GAAG,IAAI,MAAM,EAAE,GAAG,EAAE,EAAE;AACzD,YAAY,KAAK,IAAI,MAAM,GAAG,WAAW,EAAE,MAAM,IAAI,SAAS,EAAE,MAAM,EAAE,EAAE;AAC1E,cAAc,MAAM,GAAG,GAAG,OAAO,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC;AAChF,cAAc,QAAQ,CAAC,IAAI,CAACiB,KAAC,CAACE,YAAQ,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE;AACxG,gBAAgB,WAAW,EAAE,MAAM;AACnC,gBAAgB,IAAI;AACpB,gBAAgB,WAAW,EAAE,cAAc,GAAGnB,SAAK,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC;AAChF,gBAAgB,KAAK,EAAE,YAAY,CAAC,GAAG,EAAE,MAAM,CAAC;AAChD,gBAAgB,QAAQ,EAAE,GAAG;AAC7B,eAAe,CAAC,CAAC,CAAC,CAAC;AACnB,aAAa;AACb,WAAW;AACX,SAAS;AACT,QAAQ,OAAO,QAAQ,CAAC;AACxB,OAAO,CAAC;AACR,MAAM,MAAM,WAAW,GAAG,MAAM;AAChC,QAAQ,MAAM,KAAK,GAAGoB,2BAAuB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AAClE,QAAQ,MAAM,QAAQ,GAAG,WAAW,EAAE,CAAC;AACvC,QAAQ,OAAO;AACf,UAAUH,KAAC,CAAC,KAAK,EAAE;AACnB,YAAY,KAAK,EAAEjB,SAAK,CAAC,UAAU,CAAC;AACpC,YAAY,GAAG,EAAE,QAAQ;AACzB,WAAW,EAAE,CAACqB,eAAQ,CAAC,KAAK,CAAC,GAAG;AAChC,YAAY,OAAO,EAAE,MAAM,QAAQ;AACnC,WAAW,GAAG,QAAQ,CAAC;AACvB,SAAS,CAAC;AACV,OAAO,CAAC;AACR,MAAM,MAAM,YAAY,GAAG,MAAM;AACjC,QAAQ,MAAM,SAAS,GAAGD,2BAAuB,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAC1E,QAAQ,MAAM,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,GAAG,gBAAgB,EAAE,CAAC;AAC9E,QAAQ,MAAM,KAAK,GAAG,WAAW,EAAE,CAAC;AACpC,QAAQ,OAAOH,KAAC,CAAC,KAAK,EAAE;AACxB,UAAU,GAAG,EAAE,CAAC;AAChB,UAAU,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;AAChC,UAAU,IAAI,EAAE,KAAK,CAAC,IAAI;AAC1B,SAAS,EAAE;AACX,UAAUA,KAAC,CAAC,SAAS,EAAE;AACvB,YAAY,KAAK,EAAE,KAAK,CAAC,SAAS;AAClC,YAAY,KAAK,EAAEjB,SAAK,CAAC,WAAW,CAAC;AACrC,YAAY,QAAQ;AACpB,YAAY,GAAG,EAAE,SAAS;AAC1B,WAAW,EAAE,CAACqB,eAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,KAAK,EAAE,GAAG,KAAK,CAAC;AACrE,UAAU,mBAAmB;AAC7B,UAAU,iBAAiB;AAC3B,SAAS,CAAC,CAAC;AACX,OAAO,CAAC;AACR,MAAM,OAAO,YAAY,CAAC;AAC1B,KAAK;AACL,GAAG,CAAC,CAAC;AACL;;;;"}