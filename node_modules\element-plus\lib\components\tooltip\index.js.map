{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/tooltip/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Tooltip from './src/tooltip.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTooltip: SFCWithInstall<typeof Tooltip> = withInstall(Tooltip)\nexport * from './src/tooltip'\nexport * from './src/trigger'\nexport * from './src/content'\nexport * from './src/constants'\nexport default ElTooltip\n"], "names": ["withInstall", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;AAEY,MAAC,SAAS,GAAGA,mBAAW,CAACC,oBAAO;;;;;;;;;;;;;"}