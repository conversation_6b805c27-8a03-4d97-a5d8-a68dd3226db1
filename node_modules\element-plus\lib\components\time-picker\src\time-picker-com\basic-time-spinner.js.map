{"version": 3, "file": "basic-time-spinner.js", "sources": ["../../../../../../../packages/components/time-picker/src/time-picker-com/basic-time-spinner.vue"], "sourcesContent": ["<template>\n  <div :class=\"[ns.b('spinner'), { 'has-seconds': showSeconds }]\">\n    <template v-if=\"!arrowControl\">\n      <el-scrollbar\n        v-for=\"item in spinnerItems\"\n        :key=\"item\"\n        :ref=\"(scrollbar: unknown) => setRef(scrollbar as any, item)\"\n        :class=\"ns.be('spinner', 'wrapper')\"\n        wrap-style=\"max-height: inherit;\"\n        :view-class=\"ns.be('spinner', 'list')\"\n        noresize\n        tag=\"ul\"\n        @mouseenter=\"emitSelectRange(item)\"\n        @mousemove=\"adjustCurrentSpinner(item)\"\n      >\n        <li\n          v-for=\"(disabled, key) in timeList[item]\"\n          :key=\"key\"\n          :class=\"[\n            ns.be('spinner', 'item'),\n            ns.is('active', key === timePartials[item]),\n            ns.is('disabled', disabled),\n          ]\"\n          @click=\"handleClick(item, { value: key, disabled })\"\n        >\n          <template v-if=\"item === 'hours'\">\n            {{ ('0' + (amPmMode ? key % 12 || 12 : key)).slice(-2)\n            }}{{ getAmPmFlag(key) }}\n          </template>\n          <template v-else>\n            {{ ('0' + key).slice(-2) }}\n          </template>\n        </li>\n      </el-scrollbar>\n    </template>\n    <template v-if=\"arrowControl\">\n      <div\n        v-for=\"item in spinnerItems\"\n        :key=\"item\"\n        :class=\"[ns.be('spinner', 'wrapper'), ns.is('arrow')]\"\n        @mouseenter=\"emitSelectRange(item)\"\n      >\n        <el-icon\n          v-repeat-click=\"onDecrement\"\n          :class=\"['arrow-up', ns.be('spinner', 'arrow')]\"\n        >\n          <arrow-up />\n        </el-icon>\n        <el-icon\n          v-repeat-click=\"onIncrement\"\n          :class=\"['arrow-down', ns.be('spinner', 'arrow')]\"\n        >\n          <arrow-down />\n        </el-icon>\n        <ul :class=\"ns.be('spinner', 'list')\">\n          <li\n            v-for=\"(time, key) in arrowControlTimeList[item]\"\n            :key=\"key\"\n            :class=\"[\n              ns.be('spinner', 'item'),\n              ns.is('active', time === timePartials[item]),\n              ns.is('disabled', timeList[item][time!]),\n            ]\"\n          >\n            <template v-if=\"isNumber(time)\">\n              <template v-if=\"item === 'hours'\">\n                {{ ('0' + (amPmMode ? time % 12 || 12 : time)).slice(-2)\n                }}{{ getAmPmFlag(time) }}\n              </template>\n              <template v-else>\n                {{ ('0' + time).slice(-2) }}\n              </template>\n            </template>\n          </li>\n        </ul>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, nextTick, onMounted, ref, unref, watch } from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { vRepeatClick } from '@element-plus/directives'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport ElIcon from '@element-plus/components/icon'\nimport { ArrowDown, ArrowUp } from '@element-plus/icons-vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { getStyle, isNumber } from '@element-plus/utils'\nimport { CHANGE_EVENT } from '@element-plus/constants'\nimport {\n  DEFAULT_FORMATS_TIME,\n  PICKER_BASE_INJECTION_KEY,\n  timeUnits,\n} from '../constants'\nimport { buildTimeList } from '../utils'\nimport { basicTimeSpinnerProps } from '../props/basic-time-spinner'\nimport { getTimeLists } from '../composables/use-time-picker'\n\nimport type { Ref } from 'vue'\nimport type { ScrollbarInstance } from '@element-plus/components/scrollbar'\nimport type { TimeUnit } from '../constants'\nimport type { TimeList } from '../utils'\n\nconst props = defineProps(basicTimeSpinnerProps)\nconst pickerBase = inject(PICKER_BASE_INJECTION_KEY) as any\nconst { isRange, format } = pickerBase.props\nconst emit = defineEmits([CHANGE_EVENT, 'select-range', 'set-option'])\n\nconst ns = useNamespace('time')\n\nconst { getHoursList, getMinutesList, getSecondsList } = getTimeLists(\n  props.disabledHours,\n  props.disabledMinutes,\n  props.disabledSeconds\n)\n\n// data\nlet isScrolling = false\n\nconst currentScrollbar = ref<TimeUnit>()\nconst listHoursRef = ref<ScrollbarInstance>()\nconst listMinutesRef = ref<ScrollbarInstance>()\nconst listSecondsRef = ref<ScrollbarInstance>()\nconst listRefsMap: Record<TimeUnit, Ref<ScrollbarInstance | undefined>> = {\n  hours: listHoursRef,\n  minutes: listMinutesRef,\n  seconds: listSecondsRef,\n}\n\n// computed\nconst spinnerItems = computed(() => {\n  return props.showSeconds ? timeUnits : timeUnits.slice(0, 2)\n})\n\nconst timePartials = computed<Record<TimeUnit, number>>(() => {\n  const { spinnerDate } = props\n  const hours = spinnerDate.hour()\n  const minutes = spinnerDate.minute()\n  const seconds = spinnerDate.second()\n  return { hours, minutes, seconds }\n})\n\nconst timeList = computed(() => {\n  const { hours, minutes } = unref(timePartials)\n  const { role, spinnerDate } = props\n  const compare = !isRange ? spinnerDate : undefined\n  return {\n    hours: getHoursList(role, compare),\n    minutes: getMinutesList(hours, role, compare),\n    seconds: getSecondsList(hours, minutes, role, compare),\n  }\n})\n\nconst arrowControlTimeList = computed<Record<TimeUnit, TimeList>>(() => {\n  const { hours, minutes, seconds } = unref(timePartials)\n\n  return {\n    hours: buildTimeList(hours, 23),\n    minutes: buildTimeList(minutes, 59),\n    seconds: buildTimeList(seconds, 59),\n  }\n})\n\nconst debouncedResetScroll = debounce((type) => {\n  isScrolling = false\n  adjustCurrentSpinner(type)\n}, 200)\n\nconst getAmPmFlag = (hour: number) => {\n  const shouldShowAmPm = !!props.amPmMode\n  if (!shouldShowAmPm) return ''\n  const isCapital = props.amPmMode === 'A'\n  // todo locale\n  let content = hour < 12 ? ' am' : ' pm'\n  if (isCapital) content = content.toUpperCase()\n  return content\n}\n\nconst emitSelectRange = (type: TimeUnit) => {\n  let range = [0, 0]\n  if (!format || format === DEFAULT_FORMATS_TIME) {\n    switch (type) {\n      case 'hours':\n        range = [0, 2]\n        break\n      case 'minutes':\n        range = [3, 5]\n        break\n      case 'seconds':\n        range = [6, 8]\n        break\n    }\n  }\n  const [left, right] = range\n\n  emit('select-range', left, right)\n  currentScrollbar.value = type\n}\n\nconst adjustCurrentSpinner = (type: TimeUnit) => {\n  adjustSpinner(type, unref(timePartials)[type])\n}\n\nconst adjustSpinners = () => {\n  adjustCurrentSpinner('hours')\n  adjustCurrentSpinner('minutes')\n  adjustCurrentSpinner('seconds')\n}\n\nconst getScrollbarElement = (el: HTMLElement) =>\n  el.querySelector(`.${ns.namespace.value}-scrollbar__wrap`) as HTMLElement\n\nconst adjustSpinner = (type: TimeUnit, value: number) => {\n  if (props.arrowControl) return\n  const scrollbar = unref(listRefsMap[type])\n  if (scrollbar && scrollbar.$el) {\n    getScrollbarElement(scrollbar.$el).scrollTop = Math.max(\n      0,\n      value * typeItemHeight(type)\n    )\n  }\n}\n\nconst typeItemHeight = (type: TimeUnit): number => {\n  const scrollbar = unref(listRefsMap[type])\n  const listItem = scrollbar?.$el.querySelector('li')\n  if (listItem) {\n    return Number.parseFloat(getStyle(listItem, 'height')) || 0\n  }\n  return 0\n}\n\nconst onIncrement = () => {\n  scrollDown(1)\n}\n\nconst onDecrement = () => {\n  scrollDown(-1)\n}\n\nconst scrollDown = (step: number) => {\n  if (!currentScrollbar.value) {\n    emitSelectRange('hours')\n  }\n\n  const label = currentScrollbar.value!\n  const now = unref(timePartials)[label]\n  const total = currentScrollbar.value === 'hours' ? 24 : 60\n  const next = findNextUnDisabled(label, now, step, total)\n\n  modifyDateField(label, next)\n  adjustSpinner(label, next)\n  nextTick(() => emitSelectRange(label))\n}\n\nconst findNextUnDisabled = (\n  type: TimeUnit,\n  now: number,\n  step: number,\n  total: number\n) => {\n  let next = (now + step + total) % total\n  const list = unref(timeList)[type]\n  while (list[next] && next !== now) {\n    next = (next + step + total) % total\n  }\n  return next\n}\n\nconst modifyDateField = (type: TimeUnit, value: number) => {\n  const list = unref(timeList)[type]\n  const isDisabled = list[value]\n  if (isDisabled) return\n\n  const { hours, minutes, seconds } = unref(timePartials)\n\n  let changeTo\n  switch (type) {\n    case 'hours':\n      changeTo = props.spinnerDate.hour(value).minute(minutes).second(seconds)\n      break\n    case 'minutes':\n      changeTo = props.spinnerDate.hour(hours).minute(value).second(seconds)\n      break\n    case 'seconds':\n      changeTo = props.spinnerDate.hour(hours).minute(minutes).second(value)\n      break\n  }\n  emit(CHANGE_EVENT, changeTo)\n}\n\nconst handleClick = (\n  type: TimeUnit,\n  { value, disabled }: { value: number; disabled: boolean }\n) => {\n  if (!disabled) {\n    modifyDateField(type, value)\n    emitSelectRange(type)\n    adjustSpinner(type, value)\n  }\n}\n\nconst handleScroll = (type: TimeUnit) => {\n  const scrollbar = unref(listRefsMap[type])\n  if (!scrollbar) return\n\n  isScrolling = true\n  debouncedResetScroll(type)\n  const value = Math.min(\n    Math.round(\n      (getScrollbarElement(scrollbar.$el).scrollTop -\n        (scrollBarHeight(type) * 0.5 - 10) / typeItemHeight(type) +\n        3) /\n        typeItemHeight(type)\n    ),\n    type === 'hours' ? 23 : 59\n  )\n  modifyDateField(type, value)\n}\n\nconst scrollBarHeight = (type: TimeUnit) => {\n  return unref(listRefsMap[type])!.$el.offsetHeight\n}\n\nconst bindScrollEvent = () => {\n  const bindFunction = (type: TimeUnit) => {\n    const scrollbar = unref(listRefsMap[type])\n    if (scrollbar && scrollbar.$el) {\n      getScrollbarElement(scrollbar.$el).onscroll = () => {\n        // TODO: scroll is emitted when set scrollTop programmatically\n        // should find better solutions in the future!\n        handleScroll(type)\n      }\n    }\n  }\n  bindFunction('hours')\n  bindFunction('minutes')\n  bindFunction('seconds')\n}\n\nonMounted(() => {\n  nextTick(() => {\n    !props.arrowControl && bindScrollEvent()\n    adjustSpinners()\n    // set selection on the first hour part\n    if (props.role === 'start') emitSelectRange('hours')\n  })\n})\n\nconst setRef = (scrollbar: ScrollbarInstance | null, type: TimeUnit) => {\n  listRefsMap[type].value = scrollbar ?? undefined\n}\n\nemit('set-option', [`${props.role}_scrollDown`, scrollDown])\nemit('set-option', [`${props.role}_emitSelectRange`, emitSelectRange])\n\nwatch(\n  () => props.spinnerDate,\n  () => {\n    if (isScrolling) return\n    adjustSpinners()\n  }\n)\n</script>\n"], "names": ["inject", "PICKER_BASE_INJECTION_KEY", "useNamespace", "getTimeLists", "ref", "computed", "timeUnits", "unref", "buildTimeList", "debounce", "DEFAULT_FORMATS_TIME", "getStyle", "nextTick", "CHANGE_EVENT", "onMounted", "watch", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_Fragment", "_renderList", "_createBlock", "ElScrollbar"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAyGA,IAAM,MAAA,UAAA,GAAaA,WAAOC,mCAAyB,CAAA,CAAA;AACnD,IAAA,MAAM,EAAE,OAAA,EAAS,MAAO,EAAA,GAAI,UAAW,CAAA,KAAA,CAAA;AAGvC,IAAM,MAAA,EAAA,GAAKC,mBAAa,MAAM,CAAA,CAAA;AAE9B,IAAA,MAAM,EAAE,YAAA,EAAc,cAAgB,EAAA,cAAA,EAAmB,GAAAC,0BAAA,CAAA,KAAA,CAAA,aAAA,EAAA,KAAA,CAAA,eAAA,EAAA,KAAA,CAAA,eAAA,CAAA,CAAA;AAAA,IAAA,IACjD,WAAA,GAAA,KAAA,CAAA;AAAA,IAAA,MACA,gBAAA,GAAAC,OAAA,EAAA,CAAA;AAAA,IAAA,MACA,YAAA,GAAAA,OAAA,EAAA,CAAA;AAAA,IACR,MAAA,cAAA,GAAAA,OAAA,EAAA,CAAA;AAGA,IAAA,MAAkB,cAAA,GAAAA,OAAA,EAAA,CAAA;AAElB,IAAA,MAAM;AACN,MAAA,mBAAqB;AACrB,MAAA,uBAA8C;AAC9C,MAAA,uBAA8C;AAC9C,KAAA,CAAA;AAA0E,IAAA,MACjE,YAAA,GAAAC,YAAA,CAAA,MAAA;AAAA,MACP,OAAS,KAAA,CAAA,WAAA,GAAAC,mBAAA,GAAAA,mBAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AAAA,KAAA,CACT,CAAS;AAAA,IACX,MAAA,YAAA,GAAAD,YAAA,CAAA,MAAA;AAGA,MAAM,MAAA,EAAA,WAAe,UAAe,CAAA;AAClC,MAAA,MAAA,QAA2B,WAAA,CAAA,IAAA,EAAA,CAAA;AAAgC,MAC5D,MAAA,OAAA,GAAA,WAAA,CAAA,MAAA,EAAA,CAAA;AAED,MAAM,MAAA,OAAA,GAAA,YAAkD,MAAM,EAAA,CAAA;AAC5D,MAAM,OAAA,gBAAkB,EAAA,OAAA,EAAA,CAAA;AACxB,KAAM,CAAA,CAAA;AACN,IAAM,MAAA,QAAA,GAAAA,mBAA6B;AACnC,MAAM,MAAA,EAAA,KAAA,EAAA,qBAA6B,CAAA,YAAA,CAAA,CAAA;AACnC,MAAO,MAAA,EAAA,IAAS,EAAA,WAAS,EAAQ,GAAA,KAAA,CAAA;AAAA,MAClC,MAAA,OAAA,GAAA,CAAA,OAAA,GAAA,WAAA,GAAA,KAAA,CAAA,CAAA;AAED,MAAM,OAAA;AACJ,QAAA,KAAQ,EAAA,YAAe,CAAA,IAAA,SAAsB,CAAA;AAC7C,QAAM,OAAQ,EAAA,cAAA,CAAY,KAAI,EAAA,IAAA,EAAA,OAAA,CAAA;AAC9B,QAAM,OAAA,EAAA,cAAW,CAAA,KAAwB,EAAA,OAAA,EAAA,IAAA,EAAA,OAAA,CAAA;AACzC,OAAO,CAAA;AAAA,KACL,CAAA,CAAA;AAAiC,IAAA,MACxB,oBAAA,GAAsBA,YAAA,CAAA,MAAa;AAAA,MAAA,MACnC,EAAA,KAAA,EAAA,OAAA,EAAA,OAAsB,EAAA,GAAAE,SAAA,CAAS,YAAa,CAAA,CAAA;AAAA,MACvD,OAAA;AAAA,QACD,KAAA,EAAAC,mBAAA,CAAA,KAAA,EAAA,EAAA,CAAA;AAED,QAAM,OAAA,EAAAA,mBAAA,CAAA,WAA4D,CAAM;AACtE,QAAA,OAAe,EAAAA,mBAAS,CAAQ,OAAA,EAAA,EAAI;AAEpC,OAAO,CAAA;AAAA,KACL,CAAA,CAAA;AAA8B,IAC9B,MAAA,oBAAuB,GAAAC,sBAAW,CAAA,CAAA,IAAA,KAAA;AAAA,MAClC,WAAS,GAAc,KAAA,CAAA;AAAW,MACpC,oBAAA,CAAA,IAAA,CAAA,CAAA;AAAA,KACD,EAAA,GAAA,CAAA,CAAA;AAED,IAAM,MAAA,WAAA,GAAA,CAAA,IAAA,KAAgC;AACpC,MAAc,MAAA,cAAA,GAAA,CAAA,CAAA,KAAA,CAAA,QAAA,CAAA;AACd,MAAA,IAAA,CAAA,cAAA;AAAyB,QACrB,OAAA,EAAA,CAAA;AAEN,MAAM,MAAA,SAAA,GAAc,KAAkB,CAAA,QAAA,KAAA,GAAA,CAAA;AACpC,MAAM,IAAA,OAAA,GAAA,IAAA,GAAA,EAAiB,GAAQ,KAAA,GAAA,KAAA,CAAA;AAC/B,MAAI,IAAA;AACJ,QAAM,OAAA,GAAA,mBAA+B,EAAA,CAAA;AAErC,MAAI,OAAA,OAAU,CAAO;AACrB,KAAI,CAAA;AACJ,IAAO,MAAA,eAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACT,IAAA,KAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AAEA,MAAM,IAAA,CAAA,MAAA,IAAA,MAAA,KAAsCC,8BAAA,EAAA;AAC1C,QAAI,QAAA,IAAS;AACb,UAAI,KAAW,OAAA;AACb,YAAA,KAAc,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AAAA,YACP,MAAA;AACH,UAAQ,KAAA,SAAI;AACZ,YAAA,KAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AAAA,YACG,MAAA;AACH,UAAQ,KAAA,SAAI;AACZ,YAAA,KAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AAAA,YACG,MAAA;AACH,SAAQ;AACR,OAAA;AAAA,MACJ,MAAA,CAAA,IAAA,EAAA,KAAA,CAAA,GAAA,KAAA,CAAA;AAAA,MACF,IAAA,CAAA,cAAA,EAAA,IAAA,EAAA,KAAA,CAAA,CAAA;AACA,MAAM,gBAAO,CAAA,KAAS,GAAA,IAAA,CAAA;AAEtB,KAAK,CAAA;AACL,IAAA,MAAA,oBAAyB,GAAA,CAAA,IAAA,KAAA;AAAA,MAC3B,aAAA,CAAA,IAAA,EAAAH,SAAA,CAAA,YAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,cAAoB,GAAA,MAAkB;AAAO,MAC/C,oBAAA,CAAA,OAAA,CAAA,CAAA;AAEA,MAAA,qBAAuB,SAAM,CAAA,CAAA;AAC3B,MAAA,oBAAA,CAAqB,SAAO,CAAA,CAAA;AAC5B,KAAA,CAAA;AACA,IAAA,MAAA,mBAA8B,GAAA,CAAA,EAAA,KAAA,EAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,SAAA,CAAA,KAAA,CAAA,gBAAA,CAAA,CAAA,CAAA;AAAA,IAChC,MAAA,aAAA,GAAA,CAAA,IAAA,EAAA,KAAA,KAAA;AAEA,MAAM,IAAA,KAAA,CAAA,YAAA;AAGN,QAAM,OAAA;AACJ,MAAA,eAAwB,GAAAA,SAAA,CAAA,WAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACxB,MAAA,IAAA,SAAkB,IAAA,SAAkB,CAAA,GAAA,EAAA;AACpC,QAAI,6BAA4B,CAAA,GAAA,CAAA,CAAA,SAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,KAAA,GAAA,cAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AAC9B,OAAA;AAAoD,KAClD,CAAA;AAAA,IACA,MAAA,2BAA2B;AAAA,MAC7B,MAAA,SAAA,GAAAA,SAAA,CAAA,WAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AAAA,MACF,MAAA,QAAA,GAAA,SAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,SAAA,CAAA,GAAA,CAAA,aAAA,CAAA,IAAA,CAAA,CAAA;AAAA,MACF,IAAA,QAAA,EAAA;AAEA,QAAM,OAAA,MAAA,CAAA,UAA6C,CAAAI,cAAA,CAAA,QAAA,EAAA,QAAA,CAAA,CAAA,IAAA,CAAA,CAAA;AACjD,OAAA;AACA,MAAA,OAAiB,CAAA,CAAA;AACjB,KAAA,CAAA;AACE,IAAA,MAAA,cAAyB,MAAA;AAAiC,MAC5D,UAAA,CAAA,CAAA,CAAA,CAAA;AACA,KAAO,CAAA;AAAA,IACT,MAAA,WAAA,GAAA,MAAA;AAEA,MAAA;AACE,KAAA,CAAA;AAAY,IACd,MAAA,UAAA,GAAA,CAAA,IAAA,KAAA;AAEA,MAAA,IAAM,iBAAoB,CAAA,KAAA,EAAA;AACxB,QAAA,eAAa,CAAA,OAAA,CAAA,CAAA;AAAA,OACf;AAEA,MAAM,MAAA,KAAA,GAAA,gBAA+B,CAAA,KAAA,CAAA;AACnC,MAAI,kCAAyB,CAAA,CAAA,KAAA,CAAA,CAAA;AAC3B,MAAA,MAAA,KAAA,GAAA,gBAAuB,CAAA,KAAA,KAAA,OAAA,GAAA,EAAA,GAAA,EAAA,CAAA;AAAA,MACzB,MAAA,IAAA,GAAA,kBAAA,CAAA,KAAA,EAAA,GAAA,EAAA,IAAA,EAAA,KAAA,CAAA,CAAA;AAEA,MAAA,eAA+B,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAC/B,MAAA,aAAY,CAAA,KAAkB,EAAA,IAAA,CAAA,CAAA;AAC9B,MAAAC,YAAc,CAAA,MAAA,eAAA,CAAiB,KAAU,CAAA,CAAA,CAAA;AACzC,KAAA,CAAA;AAEA,IAAA,MAAA,qBAA2B,CAAA,IAAA,EAAA,GAAA,EAAA,IAAA,EAAA,KAAA,KAAA;AAC3B,MAAA,IAAA,IAAA,GAAA,CAAA,UAAyB,GAAA,KAAA,IAAA,KAAA,CAAA;AACzB,MAAS,MAAA,IAAA,GAAAL,SAAsB,CAAA,QAAA,CAAA,CAAA,IAAA,CAAA,CAAA;AAAM,MACvC,OAAA,IAAA,CAAA,IAAA,CAAA,IAAA,IAAA,KAAA,GAAA,EAAA;AAEA,QAAA,IAA2B,GAAA,CAAA,IAAA,GAAA,IAAA,GAAA,KAEzB,IAAA;AAIA,OAAI;AACJ,MAAA,OAAa,IAAA,CAAA;AACb,KAAA,CAAA;AACE,IAAQ,MAAA,uBAAuB,EAAA,KAAA,KAAA;AAAA,MACjC,MAAA,IAAA,GAAAA,SAAA,CAAA,QAAA,CAAA,CAAA,IAAA,CAAA,CAAA;AACA,MAAO,MAAA,UAAA,GAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MACT,IAAA,UAAA;AAEA,QAAM,OAAA;AACJ,MAAA,MAAM,EAAO,KAAA,EAAA,OAAc,EAAA,OAAM,EAAA,GAAAA,SAAA,CAAA,YAAA,CAAA,CAAA;AACjC,MAAM,IAAA,QAAA,CAAA;AACN,MAAA,QAAgB,IAAA;AAEhB,QAAA,YAAe;AAEf,UAAI,QAAA,GAAA,KAAA,CAAA,WAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA;AACJ,UAAA,MAAc;AAAA,QACZ,KAAK,SAAA;AACH,UAAW,QAAA,GAAA,KAAA,CAAM,YAAY,IAAK,CAAA,KAAK,EAAE,MAAO,CAAA,KAAA,CAAA,CAAO,MAAE,CAAA,OAAc,CAAA,CAAA;AACvE,UAAA,MAAA;AAAA,QACF,KAAK,SAAA;AACH,UAAW,QAAA,GAAA,KAAA,CAAM,YAAY,IAAK,CAAA,KAAK,EAAE,MAAO,CAAA,OAAO,CAAA,CAAA,MAAc,CAAA,KAAA,CAAA,CAAA;AACrE,UAAA,MAAA;AAAA,OAAA;AAEA,MAAW,IAAA,CAAAM,kBAAA,EAAA;AACX,KAAA,CAAA;AAAA,IACJ,MAAA,WAAA,GAAA,CAAA,IAAA,EAAA,EAAA,KAAA,EAAA,QAAA,EAAA,KAAA;AACA,MAAA,IAAA,CAAK;AAAsB,QAC7B,eAAA,CAAA,IAAA,EAAA,KAAA,CAAA,CAAA;AAEA,QAAA,gBAAoB,IAClB,CAAA,CAAA;AAGA,QAAA,aAAe,CAAA,IAAA,EAAA,KAAA,CAAA,CAAA;AACb,OAAA;AACA,KAAA,CAAA;AACA,IAAA,MAAA,YAAc,QAAW,KAAA;AAAA,MAC3B,MAAA,SAAA,GAAAN,SAAA,CAAA,WAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AAAA,MACF,IAAA,CAAA,SAAA;AAEA,QAAM,OAAA;AACJ,MAAA,WAAkB,GAAA,IAAA,CAAA;AAClB,MAAA,oBAAgB,CAAA,IAAA,CAAA,CAAA;AAEhB,MAAc,MAAA,KAAA,GAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,mBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,SAAA,GAAA,CAAA,eAAA,CAAA,IAAA,CAAA,GAAA,GAAA,GAAA,EAAA,IAAA,cAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,cAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA,KAAA,OAAA,GAAA,EAAA,GAAA,EAAA,CAAA,CAAA;AACd,MAAA,eAAA,CAAA,IAAA,EAAyB,KAAA,CAAA,CAAA;AACzB,KAAA,CAAA;AAAmB,IAAA,MACZ,eAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MAAA,OACkBA,SAAA,CAAA,WAAA,CAAA,IAAA,CAAA,CAAA,CAAA,GAAU,CAAG;AAGb,KACvB,CAAA;AAAA,IACA,MAAA,kBAAwB,MAAA;AAAA,MAC1B,MAAA,YAAA,GAAA,CAAA,IAAA,KAAA;AACA,QAAA,MAAA,qBAA2B,CAAA,WAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AAAA,QAC7B,IAAA,SAAA,IAAA,SAAA,CAAA,GAAA,EAAA;AAEA,UAAM,mBAAmB,CAAmB,SAAA,CAAA,GAAA,CAAA,CAAA,QAAA,GAAA,MAAA;AAC1C,YAAA,YAAa,CAAA,IAAA,CAAA,CAAY;AAAY,WACvC,CAAA;AAEA,SAAA;AACE,OAAM,CAAA;AACJ,MAAA,YAAkB,CAAA,OAAA,CAAA,CAAA;AAClB,MAAI,YAAA,CAAA;AACF,MAAA,YAAA,CAAA,SAAA,CAAA,CAAoB;AAGlB,KAAA,CAAA;AAAiB,IACnBO,aAAA,CAAA,MAAA;AAAA,MACFF,YAAA,CAAA,MAAA;AAAA,QACF,CAAA,KAAA,CAAA,YAAA,IAAA,eAAA,EAAA,CAAA;AACA,QAAA,cAAoB,EAAA,CAAA;AACpB,QAAA,IAAA,KAAA,CAAA,IAAsB,KAAA,OAAA;AACtB,UAAA,eAAsB,CAAA,OAAA,CAAA,CAAA;AAAA,OACxB,CAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AACE,IAAA,MAAA,MAAe,GAAA,CAAA,SAAA,EAAA,IAAA,KAAA;AACb,MAAC,0BAAsC,SAAA,IAAA,IAAA,GAAA,SAAA,GAAA,KAAA,CAAA,CAAA;AACvC,KAAe,CAAA;AAEf,IAAA,IAAA,CAAA,YAAU,EAAA,CAAA,CAAA,EAAS,KAAS,CAAA,IAAA,CAAA,WAAA,CAAA,EAAgB,UAAO,CAAA,CAAA,CAAA;AAAA,IAAA,IACpD,CAAA,YAAA,EAAA,CAAA,CAAA,EAAA,KAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,EAAA,eAAA,CAAA,CAAA,CAAA;AAAA,IACHG,SAAC,CAAA,MAAA,KAAA,CAAA,WAAA,EAAA,MAAA;AAED,MAAM,IAAA,WAAU;AACd,QAAY,OAAA;AAA2B,MACzC,cAAA,EAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AACA,IAAA,oBAAoB;AAEpB,MAAA,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,aACc,EAAAC,kBAAA,CAAA,CAAAC,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,EAAA,EAAA,aAAA,EAAA,IAAA,CAAA,WAAA,EAAA,CAAA,CAAA;AAAA,OACN,EAAA;AACJ,QAAA,CAAA,IAAiB,CAAA,YAAA,IAAAH,aAAA,CAAA,IAAA,CAAA,EAAAC,sBAAA,CAAAG,YAAA,EAAA,EAAA,GAAA,EAAA,CAAA,EAAA,EAAAC,cAAA,CAAAF,SAAA,CAAA,YAAA,CAAA,EAAA,CAAA,IAAA,KAAA;AACjB,UAAe,OAAAH,aAAA,EAAA,EAAAM,eAAA,CAAAH,SAAA,CAAAI,mBAAA,CAAA,EAAA;AAAA,YACjB,GAAA,EAAA,IAAA;AAAA,YACF,OAAA,EAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}