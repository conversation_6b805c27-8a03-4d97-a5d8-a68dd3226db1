{"version": 3, "file": "picker.js", "sources": ["../../../../../../../packages/components/time-picker/src/common/picker.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"refPopper\"\n    :visible=\"pickerVisible\"\n    effect=\"light\"\n    pure\n    trigger=\"click\"\n    v-bind=\"$attrs\"\n    role=\"dialog\"\n    teleported\n    :transition=\"`${nsDate.namespace.value}-zoom-in-top`\"\n    :popper-class=\"[`${nsDate.namespace.value}-picker__popper`, popperClass]\"\n    :popper-options=\"elPopperOptions\"\n    :fallback-placements=\"fallbackPlacements\"\n    :gpu-acceleration=\"false\"\n    :placement=\"placement\"\n    :stop-popper-mouse-event=\"false\"\n    :hide-after=\"0\"\n    persistent\n    @before-show=\"onBeforeShow\"\n    @show=\"onShow\"\n    @hide=\"onHide\"\n  >\n    <template #default>\n      <el-input\n        v-if=\"!isRangeInput\"\n        :id=\"(id as string | undefined)\"\n        ref=\"inputRef\"\n        container-role=\"combobox\"\n        :model-value=\"(displayValue as string)\"\n        :name=\"(name as string | undefined)\"\n        :size=\"pickerSize\"\n        :disabled=\"pickerDisabled\"\n        :placeholder=\"placeholder\"\n        :class=\"[nsDate.b('editor'), nsDate.bm('editor', type), $attrs.class]\"\n        :style=\"$attrs.style\"\n        :readonly=\"\n          !editable ||\n          readonly ||\n          isDatesPicker ||\n          isMonthsPicker ||\n          isYearsPicker ||\n          type === 'week'\n        \"\n        :aria-label=\"ariaLabel\"\n        :tabindex=\"tabindex\"\n        :validate-event=\"false\"\n        @input=\"onUserInput\"\n        @focus=\"handleFocus\"\n        @blur=\"handleBlur\"\n        @keydown=\"handleKeydownInput\"\n        @change=\"handleChange\"\n        @mousedown=\"onMouseDownInput\"\n        @mouseenter=\"onMouseEnter\"\n        @mouseleave=\"onMouseLeave\"\n        @touchstart.passive=\"onTouchStartInput\"\n        @click.stop\n      >\n        <template #prefix>\n          <el-icon\n            v-if=\"triggerIcon\"\n            :class=\"nsInput.e('icon')\"\n            @mousedown.prevent=\"onMouseDownInput\"\n            @touchstart.passive=\"onTouchStartInput\"\n          >\n            <component :is=\"triggerIcon\" />\n          </el-icon>\n        </template>\n        <template #suffix>\n          <el-icon\n            v-if=\"showClose && clearIcon\"\n            :class=\"`${nsInput.e('icon')} clear-icon`\"\n            @mousedown.prevent=\"NOOP\"\n            @click=\"onClearIconClick\"\n          >\n            <component :is=\"clearIcon\" />\n          </el-icon>\n        </template>\n      </el-input>\n      <picker-range-trigger\n        v-else\n        :id=\"(id as string[] | undefined)\"\n        ref=\"inputRef\"\n        :model-value=\"displayValue\"\n        :name=\"(name as string[] | undefined)\"\n        :disabled=\"pickerDisabled\"\n        :readonly=\"!editable || readonly\"\n        :start-placeholder=\"startPlaceholder\"\n        :end-placeholder=\"endPlaceholder\"\n        :class=\"rangeInputKls\"\n        :style=\"$attrs.style\"\n        :aria-label=\"ariaLabel\"\n        :tabindex=\"tabindex\"\n        autocomplete=\"off\"\n        role=\"combobox\"\n        @click=\"onMouseDownInput\"\n        @focus=\"handleFocus\"\n        @blur=\"handleBlur\"\n        @start-input=\"handleStartInput\"\n        @start-change=\"handleStartChange\"\n        @end-input=\"handleEndInput\"\n        @end-change=\"handleEndChange\"\n        @mousedown=\"onMouseDownInput\"\n        @mouseenter=\"onMouseEnter\"\n        @mouseleave=\"onMouseLeave\"\n        @touchstart.passive=\"onTouchStartInput\"\n        @keydown=\"handleKeydownInput\"\n      >\n        <template #prefix>\n          <el-icon\n            v-if=\"triggerIcon\"\n            :class=\"[nsInput.e('icon'), nsRange.e('icon')]\"\n          >\n            <component :is=\"triggerIcon\" />\n          </el-icon>\n        </template>\n        <template #range-separator>\n          <slot name=\"range-separator\">\n            <span :class=\"nsRange.b('separator')\">{{ rangeSeparator }}</span>\n          </slot>\n        </template>\n        <template #suffix>\n          <el-icon\n            v-if=\"clearIcon\"\n            :class=\"clearIconKls\"\n            @mousedown.prevent=\"NOOP\"\n            @click=\"onClearIconClick\"\n          >\n            <component :is=\"clearIcon\" />\n          </el-icon>\n        </template>\n      </picker-range-trigger>\n    </template>\n    <template #content>\n      <slot\n        :visible=\"pickerVisible\"\n        :actual-visible=\"pickerActualVisible\"\n        :parsed-value=\"parsedValue\"\n        :format=\"format\"\n        :date-format=\"dateFormat\"\n        :time-format=\"timeFormat\"\n        :unlink-panels=\"unlinkPanels\"\n        :type=\"type\"\n        :default-value=\"defaultValue\"\n        :show-now=\"showNow\"\n        :show-week-number=\"showWeekNumber\"\n        @pick=\"onPick\"\n        @select-range=\"setSelectionRange\"\n        @set-picker-option=\"onSetPickerOption\"\n        @calendar-change=\"onCalendarChange\"\n        @panel-change=\"onPanelChange\"\n        @mousedown.stop\n      />\n    </template>\n  </el-tooltip>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  inject,\n  nextTick,\n  onBeforeUnmount,\n  provide,\n  ref,\n  unref,\n  useAttrs,\n  watch,\n} from 'vue'\nimport { isEqual } from 'lodash-unified'\nimport { onClickOutside, unrefElement } from '@vueuse/core'\nimport {\n  useEmptyValues,\n  useFocusController,\n  useLocale,\n  useNamespace,\n} from '@element-plus/hooks'\nimport { useFormItem, useFormSize } from '@element-plus/components/form'\nimport ElInput from '@element-plus/components/input'\nimport ElIcon from '@element-plus/components/icon'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport { NOOP, debugWarn, isArray } from '@element-plus/utils'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { Calendar, Clock } from '@element-plus/icons-vue'\nimport { dayOrDaysToDate, formatter, parseDate, valueEquals } from '../utils'\nimport {\n  PICKER_BASE_INJECTION_KEY,\n  PICKER_POPPER_OPTIONS_INJECTION_KEY,\n} from '../constants'\nimport { timePickerDefaultProps } from './props'\nimport PickerRangeTrigger from './picker-range-trigger.vue'\n\nimport type { InputInstance } from '@element-plus/components/input'\nimport type { Dayjs } from 'dayjs'\nimport type { ComponentPublicInstance, Ref } from 'vue'\nimport type { Options } from '@popperjs/core'\nimport type {\n  DateModelType,\n  DayOrDays,\n  PickerOptions,\n  SingleOrRange,\n  TimePickerDefaultProps,\n  UserInput,\n} from './props'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\n\ndefineOptions({\n  name: 'Picker',\n})\n\nconst props = defineProps(timePickerDefaultProps)\nconst emit = defineEmits([\n  UPDATE_MODEL_EVENT,\n  CHANGE_EVENT,\n  'focus',\n  'blur',\n  'clear',\n  'calendar-change',\n  'panel-change',\n  'visible-change',\n  'keydown',\n])\nconst attrs = useAttrs()\n\nconst { lang } = useLocale()\n\nconst nsDate = useNamespace('date')\nconst nsInput = useNamespace('input')\nconst nsRange = useNamespace('range')\n\nconst { form, formItem } = useFormItem()\nconst elPopperOptions = inject(\n  PICKER_POPPER_OPTIONS_INJECTION_KEY,\n  {} as Options\n)\nconst { valueOnClear } = useEmptyValues(props, null)\n\nconst refPopper = ref<TooltipInstance>()\nconst inputRef = ref<InputInstance>()\nconst pickerVisible = ref(false)\nconst pickerActualVisible = ref(false)\nconst valueOnOpen = ref<TimePickerDefaultProps['modelValue'] | null>(null)\nlet hasJustTabExitedInput = false\n\nconst pickerDisabled = computed(() => {\n  return props.disabled || !!form?.disabled\n})\n\nconst { isFocused, handleFocus, handleBlur } = useFocusController(inputRef, {\n  disabled: pickerDisabled,\n  beforeFocus() {\n    return props.readonly\n  },\n  afterFocus() {\n    pickerVisible.value = true\n  },\n  beforeBlur(event) {\n    return (\n      !hasJustTabExitedInput && refPopper.value?.isFocusInsideContent(event)\n    )\n  },\n  afterBlur() {\n    handleChange()\n    pickerVisible.value = false\n    hasJustTabExitedInput = false\n    props.validateEvent &&\n      formItem?.validate('blur').catch((err) => debugWarn(err))\n  },\n})\n\nconst rangeInputKls = computed(() => [\n  nsDate.b('editor'),\n  nsDate.bm('editor', props.type),\n  nsInput.e('wrapper'),\n  nsDate.is('disabled', pickerDisabled.value),\n  nsDate.is('active', pickerVisible.value),\n  nsRange.b('editor'),\n  pickerSize ? nsRange.bm('editor', pickerSize.value) : '',\n  attrs.class,\n])\n\nconst clearIconKls = computed(() => [\n  nsInput.e('icon'),\n  nsRange.e('close-icon'),\n  !showClose.value ? nsRange.e('close-icon--hidden') : '',\n])\n\nwatch(pickerVisible, (val) => {\n  if (!val) {\n    userInput.value = null\n    nextTick(() => {\n      emitChange(props.modelValue)\n    })\n  } else {\n    nextTick(() => {\n      if (val) {\n        valueOnOpen.value = props.modelValue\n      }\n    })\n  }\n})\nconst emitChange = (\n  val: TimePickerDefaultProps['modelValue'] | null,\n  isClear?: boolean\n) => {\n  // determine user real change only\n  if (isClear || !valueEquals(val, valueOnOpen.value)) {\n    emit(CHANGE_EVENT, val)\n    // Set the value of valueOnOpen when clearing to avoid triggering change events multiple times.\n    isClear && (valueOnOpen.value = val)\n    props.validateEvent &&\n      formItem?.validate('change').catch((err) => debugWarn(err))\n  }\n}\nconst emitInput = (input: SingleOrRange<DateModelType> | null) => {\n  if (!valueEquals(props.modelValue, input)) {\n    let formatted\n    if (isArray(input)) {\n      formatted = input.map((item) =>\n        formatter(item, props.valueFormat, lang.value)\n      )\n    } else if (input) {\n      formatted = formatter(input, props.valueFormat, lang.value)\n    }\n    emit(UPDATE_MODEL_EVENT, input ? formatted : input, lang.value)\n  }\n}\nconst emitKeydown = (e: KeyboardEvent) => {\n  emit('keydown', e)\n}\n\nconst refInput = computed<HTMLInputElement[]>(() => {\n  if (inputRef.value) {\n    return Array.from<HTMLInputElement>(\n      inputRef.value.$el.querySelectorAll('input')\n    )\n  }\n  return []\n})\n\n// @ts-ignore\nconst setSelectionRange = (start: number, end: number, pos?: 'min' | 'max') => {\n  const _inputs = refInput.value\n  if (!_inputs.length) return\n  if (!pos || pos === 'min') {\n    _inputs[0].setSelectionRange(start, end)\n    _inputs[0].focus()\n  } else if (pos === 'max') {\n    _inputs[1].setSelectionRange(start, end)\n    _inputs[1].focus()\n  }\n}\n\nconst onPick = (date: any = '', visible = false) => {\n  pickerVisible.value = visible\n  let result\n  if (isArray(date)) {\n    result = date.map((_) => _.toDate())\n  } else {\n    // clear btn emit null\n    result = date ? date.toDate() : date\n  }\n  userInput.value = null\n  emitInput(result)\n}\n\nconst onBeforeShow = () => {\n  pickerActualVisible.value = true\n}\n\nconst onShow = () => {\n  emit('visible-change', true)\n}\n\nconst onHide = () => {\n  pickerActualVisible.value = false\n  pickerVisible.value = false\n  emit('visible-change', false)\n}\n\nconst handleOpen = () => {\n  pickerVisible.value = true\n}\n\nconst handleClose = () => {\n  pickerVisible.value = false\n}\n\nconst parsedValue = computed(() => {\n  let dayOrDays: DayOrDays\n  if (valueIsEmpty.value) {\n    if (pickerOptions.value.getDefaultValue) {\n      dayOrDays = pickerOptions.value.getDefaultValue()\n    }\n  } else {\n    if (isArray(props.modelValue)) {\n      dayOrDays = props.modelValue.map((d) =>\n        parseDate(d, props.valueFormat, lang.value)\n      ) as [Dayjs, Dayjs]\n    } else {\n      dayOrDays = parseDate(props.modelValue, props.valueFormat, lang.value)!\n    }\n  }\n\n  if (pickerOptions.value.getRangeAvailableTime) {\n    const availableResult = pickerOptions.value.getRangeAvailableTime(\n      dayOrDays!\n    )\n    if (!isEqual(availableResult, dayOrDays!)) {\n      dayOrDays = availableResult\n\n      // The result is corrected only when model-value exists\n      if (!valueIsEmpty.value) {\n        emitInput(dayOrDaysToDate(dayOrDays))\n      }\n    }\n  }\n  if (isArray(dayOrDays!) && dayOrDays.some((day) => !day)) {\n    dayOrDays = [] as unknown as DayOrDays\n  }\n  return dayOrDays!\n})\n\nconst displayValue = computed<UserInput>(() => {\n  if (!pickerOptions.value.panelReady) return ''\n  const formattedValue = formatDayjsToString(parsedValue.value)\n  if (isArray(userInput.value)) {\n    return [\n      userInput.value[0] || (formattedValue && formattedValue[0]) || '',\n      userInput.value[1] || (formattedValue && formattedValue[1]) || '',\n    ]\n  } else if (userInput.value !== null) {\n    return userInput.value\n  }\n  if (!isTimePicker.value && valueIsEmpty.value) return ''\n  if (!pickerVisible.value && valueIsEmpty.value) return ''\n  if (formattedValue) {\n    return isDatesPicker.value || isMonthsPicker.value || isYearsPicker.value\n      ? (formattedValue as Array<string>).join(', ')\n      : formattedValue\n  }\n  return ''\n})\n\nconst isTimeLikePicker = computed(() => props.type.includes('time'))\n\nconst isTimePicker = computed(() => props.type.startsWith('time'))\n\nconst isDatesPicker = computed(() => props.type === 'dates')\n\nconst isMonthsPicker = computed(() => props.type === 'months')\n\nconst isYearsPicker = computed(() => props.type === 'years')\n\nconst triggerIcon = computed(\n  () => props.prefixIcon || (isTimeLikePicker.value ? Clock : Calendar)\n)\n\nconst showClose = ref(false)\n\nconst onClearIconClick = (event: MouseEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n  if (showClose.value) {\n    event.stopPropagation()\n    // When the handleClear Function was provided, emit null will be executed inside it\n    // There is no need for us to execute emit null twice. #14752\n    if (pickerOptions.value.handleClear) {\n      pickerOptions.value.handleClear()\n    } else {\n      emitInput(valueOnClear.value)\n    }\n    emitChange(valueOnClear.value, true)\n    showClose.value = false\n    onHide()\n  }\n  emit('clear')\n}\n\nconst valueIsEmpty = computed(() => {\n  const { modelValue } = props\n  return (\n    !modelValue || (isArray(modelValue) && !modelValue.filter(Boolean).length)\n  )\n})\n\nconst onMouseDownInput = async (event: MouseEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n  if ((event.target as HTMLElement)?.tagName !== 'INPUT' || isFocused.value) {\n    pickerVisible.value = true\n  }\n}\nconst onMouseEnter = () => {\n  if (props.readonly || pickerDisabled.value) return\n  if (!valueIsEmpty.value && props.clearable) {\n    showClose.value = true\n  }\n}\nconst onMouseLeave = () => {\n  showClose.value = false\n}\n\nconst onTouchStartInput = (event: TouchEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n  if (\n    (event.touches[0].target as HTMLElement)?.tagName !== 'INPUT' ||\n    isFocused.value\n  ) {\n    pickerVisible.value = true\n  }\n}\n\nconst isRangeInput = computed(() => {\n  return props.type.includes('range')\n})\n\nconst pickerSize = useFormSize()\n\nconst popperEl = computed(() => unref(refPopper)?.popperRef?.contentRef)\n\nconst stophandle = onClickOutside(\n  inputRef as Ref<ComponentPublicInstance>,\n  (e: PointerEvent) => {\n    const unrefedPopperEl = unref(popperEl)\n    const inputEl = unrefElement(inputRef as Ref<ComponentPublicInstance>)\n    if (\n      (unrefedPopperEl &&\n        (e.target === unrefedPopperEl ||\n          e.composedPath().includes(unrefedPopperEl))) ||\n      e.target === inputEl ||\n      (inputEl && e.composedPath().includes(inputEl))\n    )\n      return\n    pickerVisible.value = false\n  }\n)\n\nonBeforeUnmount(() => {\n  stophandle?.()\n})\n\nconst userInput = ref<UserInput>(null)\n\nconst handleChange = () => {\n  if (userInput.value) {\n    const value = parseUserInputToDayjs(displayValue.value)\n    if (value) {\n      if (isValidValue(value)) {\n        emitInput(dayOrDaysToDate(value))\n        userInput.value = null\n      }\n    }\n  }\n  if (userInput.value === '') {\n    emitInput(valueOnClear.value)\n    emitChange(valueOnClear.value, true)\n    userInput.value = null\n  }\n}\n\nconst parseUserInputToDayjs = (value: UserInput) => {\n  if (!value) return null\n  return pickerOptions.value.parseUserInput!(value)\n}\n\nconst formatDayjsToString = (value: DayOrDays) => {\n  if (!value) return null\n  return pickerOptions.value.formatToString!(value)\n}\n\nconst isValidValue = (value: DayOrDays) => {\n  return pickerOptions.value.isValidValue!(value)\n}\n\nconst handleKeydownInput = async (event: Event | KeyboardEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n\n  const { code } = event as KeyboardEvent\n  emitKeydown(event as KeyboardEvent)\n  if (code === EVENT_CODE.esc) {\n    if (pickerVisible.value === true) {\n      pickerVisible.value = false\n      event.preventDefault()\n      event.stopPropagation()\n    }\n    return\n  }\n\n  if (code === EVENT_CODE.down) {\n    if (pickerOptions.value.handleFocusPicker) {\n      event.preventDefault()\n      event.stopPropagation()\n    }\n    if (pickerVisible.value === false) {\n      pickerVisible.value = true\n      await nextTick()\n    }\n    if (pickerOptions.value.handleFocusPicker) {\n      pickerOptions.value.handleFocusPicker()\n      return\n    }\n  }\n\n  if (code === EVENT_CODE.tab) {\n    hasJustTabExitedInput = true\n    return\n  }\n\n  if (code === EVENT_CODE.enter || code === EVENT_CODE.numpadEnter) {\n    if (\n      userInput.value === null ||\n      userInput.value === '' ||\n      isValidValue(parseUserInputToDayjs(displayValue.value) as DayOrDays)\n    ) {\n      handleChange()\n      pickerVisible.value = false\n    }\n    event.stopPropagation()\n    return\n  }\n\n  // if user is typing, do not let picker handle key input\n  if (userInput.value) {\n    event.stopPropagation()\n    return\n  }\n  if (pickerOptions.value.handleKeydownInput) {\n    pickerOptions.value.handleKeydownInput(event as KeyboardEvent)\n  }\n}\nconst onUserInput = (e: string) => {\n  userInput.value = e\n  // Temporary fix when the picker is dismissed and the input box\n  // is focused, just mimic the behavior of antdesign.\n  if (!pickerVisible.value) {\n    pickerVisible.value = true\n  }\n}\n\nconst handleStartInput = (event: Event) => {\n  const target = event.target as HTMLInputElement\n  if (userInput.value) {\n    userInput.value = [target.value, userInput.value[1]]\n  } else {\n    userInput.value = [target.value, null]\n  }\n}\n\nconst handleEndInput = (event: Event) => {\n  const target = event.target as HTMLInputElement\n  if (userInput.value) {\n    userInput.value = [userInput.value[0], target.value]\n  } else {\n    userInput.value = [null, target.value]\n  }\n}\n\nconst handleStartChange = () => {\n  const values = userInput.value as string[]\n  const value = parseUserInputToDayjs(values && values[0]) as Dayjs\n  const parsedVal = unref(parsedValue) as [Dayjs, Dayjs]\n  if (value && value.isValid()) {\n    userInput.value = [\n      formatDayjsToString(value) as string,\n      displayValue.value?.[1] || null,\n    ]\n    const newValue = [value, parsedVal && (parsedVal[1] || null)] as DayOrDays\n    if (isValidValue(newValue)) {\n      emitInput(dayOrDaysToDate(newValue))\n      userInput.value = null\n    }\n  }\n}\n\nconst handleEndChange = () => {\n  const values = unref(userInput) as string[]\n  const value = parseUserInputToDayjs(values && values[1]) as Dayjs\n  const parsedVal = unref(parsedValue) as [Dayjs, Dayjs]\n  if (value && value.isValid()) {\n    userInput.value = [\n      unref(displayValue)?.[0] || null,\n      formatDayjsToString(value) as string,\n    ]\n    const newValue = [parsedVal && parsedVal[0], value] as DayOrDays\n    if (isValidValue(newValue)) {\n      emitInput(dayOrDaysToDate(newValue))\n      userInput.value = null\n    }\n  }\n}\n\nconst pickerOptions = ref<Partial<PickerOptions>>({})\n// @ts-ignore\nconst onSetPickerOption = <T extends keyof PickerOptions>(\n  e: [T, PickerOptions[T]]\n) => {\n  pickerOptions.value[e[0]] = e[1]\n  pickerOptions.value.panelReady = true\n}\n\n// @ts-ignore\nconst onCalendarChange = (e: [Date, null | Date]) => {\n  emit('calendar-change', e)\n}\n\n// @ts-ignore\nconst onPanelChange = (\n  value: [Dayjs, Dayjs],\n  mode: 'month' | 'year',\n  view: unknown\n) => {\n  emit('panel-change', value, mode, view)\n}\n\nconst focus = () => {\n  inputRef.value?.focus()\n}\n\nconst blur = () => {\n  inputRef.value?.blur()\n}\n\nprovide(PICKER_BASE_INJECTION_KEY, {\n  props,\n})\n\ndefineExpose({\n  /**\n   * @description focus input box.\n   */\n  focus,\n  /**\n   * @description blur input box.\n   */\n  blur,\n  /**\n   * @description opens picker\n   */\n  handleOpen,\n  /**\n   * @description closes picker\n   */\n  handleClose,\n  /**\n   * @description pick item manually\n   */\n  onPick,\n})\n</script>\n"], "names": ["useAttrs", "useLocale", "useNamespace", "useFormItem", "inject", "PICKER_POPPER_OPTIONS_INJECTION_KEY", "useEmptyValues", "ref", "computed", "useFocusController", "debugWarn", "watch", "nextTick", "valueEquals", "CHANGE_EVENT", "isArray", "formatter", "UPDATE_MODEL_EVENT", "parseDate", "isEqual", "dayOrDaysToDate", "Clock", "Calendar", "useFormSize", "unref", "onClickOutside", "unrefElement", "onBeforeUnmount", "EVENT_CODE", "provide", "PICKER_BASE_INJECTION_KEY", "_openBlock", "_createBlock", "_unref", "ElTooltip", "_mergeProps"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;uCAkNc,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;AAcA,IAAA,MAAM,QAAQA,YAAS,EAAA,CAAA;AAEvB,IAAM,MAAA,EAAE,IAAK,EAAA,GAAIC,eAAU,EAAA,CAAA;AAE3B,IAAM,MAAA,MAAA,GAASC,qBAAa,MAAM,CAAA,CAAA;AAClC,IAAM,MAAA,OAAA,GAAUA,qBAAa,OAAO,CAAA,CAAA;AACpC,IAAM,MAAA,OAAA,GAAUA,qBAAa,OAAO,CAAA,CAAA;AAEpC,IAAA,MAAM,EAAE,IAAA,EAAM,QAAS,EAAA,GAAIC,uBAAY,EAAA,CAAA;AACvC,IAAA,MAAM,eAAkB,GAAAC,UAAA,CAAAC,6CAAA,EAAA,EAAA,CAAA,CAAA;AAAA,IACtB,MAAA,EAAA,YAAA,EAAA,GAAAC,sBAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAAA,IAAA,MACC,SAAA,GAAAC,OAAA,EAAA,CAAA;AAAA,IACH,MAAA,QAAA,GAAAA,OAAA,EAAA,CAAA;AACA,IAAA,MAAM,aAAE,GAAiBA,OAAA,CAAA,KAAA,CAAA,CAAA;AAEzB,IAAA,MAAM,mBAAiC,GAAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AACvC,IAAA,MAAM,WAAW,GAAmBA,OAAA,CAAA,IAAA,CAAA,CAAA;AACpC,IAAM,IAAA,wBAAyB,KAAA,CAAA;AAC/B,IAAM,MAAA,cAAA,GAAAC,aAA0B,MAAK;AACrC,MAAM,OAAA,KAAA,CAAA,YAAmE,CAAA,EAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,QAAA,CAAA,CAAA;AACzE,KAAA,CAAA,CAAA;AAEA,IAAM,MAAA,EAAA,SAAA,EAAA,aAA0B,UAAM,EAAA,GAAAC,0BAAA,CAAA,QAAA,EAAA;AACpC,MAAA,QAAa,EAAA,cAAA;AAAoB,MAClC,WAAA,GAAA;AAED,QAAA,OAAmB,KAAA,CAAA,QAAA,CAAA;AAAyD,OAChE;AAAA,MACV,UAAc,GAAA;AACZ,QAAA,aAAa,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,OACf;AAAA,MACA,UAAa,CAAA,KAAA,EAAA;AACX,QAAA,IAAA,EAAA,CAAA;AAAsB,QACxB,OAAA,CAAA,qBAAA,KAAA,CAAA,EAAA,GAAA,SAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA;AAEE,MAAA,SACE,GAAC;AAAoE,QAEzE,YAAA,EAAA,CAAA;AAAA,QACY,aAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AACV,QAAa,qBAAA,GAAA,KAAA,CAAA;AACb,QAAA,KAAA,CAAA,aAAsB,KAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAAC,eAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AACtB,OAAwB;AACxB,KAAM,CAAA,CAAA;AACoD,IAC5D,MAAA,aAAA,GAAAF,YAAA,CAAA,MAAA;AAAA,MACD,MAAA,CAAA,CAAA,CAAA,QAAA,CAAA;AAED,MAAM,MAAA,CAAA,EAAA,CAAA,QAAA,EAAgB,UAAe,CAAA;AAAA,MACnC,OAAO,EAAE,CAAQ,SAAA,CAAA;AAAA,MACjB,MAAO,CAAA,EAAA,CAAG,UAAU,EAAA,cAAU,CAAA,KAAA,CAAA;AAAA,MAC9B,MAAA,CAAA,GAAU,QAAS,EAAA,aAAA,CAAA,KAAA,CAAA;AAAA,MACnB,OAAO,CAAA,CAAA,CAAG,QAAY,CAAA;AAAoB,MAC1C,UAAU,GAAU,OAAA,CAAA,EAAA,CAAA,QAAA,EAAc,UAAK,CAAA,KAAA,CAAA,GAAA,EAAA;AAAA,MACvC,KAAA,CAAA,KAAkB;AAAA,KAAA,CAClB;AAAsD,IAAA,MAChD,YAAA,GAAAA,YAAA,CAAA,MAAA;AAAA,MACP,OAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AAED,MAAM,OAAA,CAAA,CAAA,CAAA;AAA8B,MAClC,CAAA,SAAU,CAAM,KAAA,GAAA,OAAA,CAAA,CAAA,CAAA,oBAAA,CAAA,GAAA,EAAA;AAAA,KAChB,CAAA,CAAA;AAAsB,IAAAG,SACX,CAAA,aAAA,EAAQ,CAAQ,GAAA,KAAA;AAA0B,MACtD,IAAA,CAAA,GAAA,EAAA;AAED,QAAM,SAAA,CAAA,KAAA,GAAgB,IAAQ,CAAA;AAC5B,QAAAC,YAAU,CAAA,MAAA;AACR,UAAA,UAAkB,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AAClB,SAAA,CAAA,CAAA;AACE,OAAA,MAAA;AAA2B,QAC7BA,YAAC,CAAA,MAAA;AAAA,UACI,IAAA,GAAA,EAAA;AACL,YAAA,WAAe,CAAA,KAAA,GAAA,KAAA,CAAA,UAAA,CAAA;AACb,WAAA;AACE,SAAA,CAAA,CAAA;AAA0B,OAC5B;AAAA,KAAA,CAAA,CAAA;AACD,IACH,MAAA,UAAA,GAAA,CAAA,GAAA,EAAA,OAAA,KAAA;AAAA,MACD,IAAA,OAAA,IAAA,CAAAC,iBAAA,CAAA,GAAA,EAAA,WAAA,CAAA,KAAA,CAAA,EAAA;AACD,QAAM,IAAA,CAAAC,kBACJ,EAAA,GAEG,CAAA,CAAA;AAEH,QAAA,uBAAgB,CAAA,KAAiB,GAAA,GAAA,CAAA,CAAA;AAC/B,QAAA,KAAK,cAAc,KAAG,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAAJ,eAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AAEtB,OAAA;AACA,KAAM,CAAA;AACsD,IAC9D,MAAA,SAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACF,IAAA,CAAAG,iBAAA,CAAA,KAAA,CAAA,UAAA,EAAA,KAAA,CAAA,EAAA;AACA,QAAM,IAAA,SAAA,CAAY;AAChB,QAAA,IAAKE,cAAA,CAAA,KAAkB,CAAA,EAAA;AACrB,UAAI,SAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,IAAA,KAAAC,eAAA,CAAA,IAAA,EAAA,KAAA,CAAA,WAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACJ,SAAI,MAAA,IAAA,OAAgB;AAClB,UAAA,SAAA,GAAYA,eAAM,CAAA,KAAA,EAAA,KAAA,CAAA,WAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,SAAA;AAC6B,QAC/C,IAAA,CAAAC,wBAAA,EAAA,KAAA,GAAA,SAAA,GAAA,KAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OAAA;AAEA,KAAA,CAAA;AAA0D,IAC5D,MAAA,WAAA,GAAA,CAAA,CAAA,KAAA;AACA,MAAA,IAAA,CAAA,SAAyB,EAAA,CAAA,CAAA,CAAA;AAAqC,KAChE,CAAA;AAAA,IACF,MAAA,QAAA,GAAAT,YAAA,CAAA,MAAA;AACA,MAAM,IAAA,QAAA,CAAA,KAAc,EAAsB;AACxC,QAAA,iBAAiB,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AAAA,OACnB;AAEA,MAAM,OAAA,EAAA,CAAA;AACJ,KAAA,CAAA,CAAA;AACE,IAAA,MAAA,iBAAa,GAAA,CAAA,KAAA,EAAA,GAAA,EAAA,GAAA,KAAA;AAAA,MAAA,MACF,OAAA,GAAA,QAAU,CAAA,KAAA,CAAA;AAAwB,MAC7C,IAAA,CAAA,OAAA,CAAA,MAAA;AAAA,QACF,OAAA;AACA,MAAA,IAAA,CAAA,GAAQ,IAAA,GAAA,KAAA,KAAA,EAAA;AAAA,QACT,OAAA,CAAA,CAAA,CAAA,CAAA,iBAAA,CAAA,KAAA,EAAA,GAAA,CAAA,CAAA;AAGD,QAAA,OAA0B,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAA;AACxB,OAAA,kBAAyB,KAAA,EAAA;AACzB,QAAI,WAAS,iBAAQ,CAAA,KAAA,EAAA,GAAA,CAAA,CAAA;AACrB,QAAI,OAAQ,CAAA,CAAA,CAAA,CAAA,KAAA,EAAQ,CAAO;AACzB,OAAA;AACA,KAAQ,CAAA;AAAS,IACnB,MAAA,MAAA,WAA0B,EAAA,EAAA,OAAA,GAAA,KAAA,KAAA;AACxB,MAAA,aAAW,CAAkB,KAAA,GAAA,OAAA,CAAA;AAC7B,MAAQ,IAAA,MAAA,CAAC;AAAQ,MACnB,IAAAO,cAAA,CAAA,IAAA,CAAA,EAAA;AAAA,QACF,MAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA;AAEA,OAAA,MAAe;AACb,QAAA,MAAA,GAAA,IAAsB,GAAA,IAAA,CAAA,MAAA,EAAA,GAAA,IAAA,CAAA;AACtB,OAAI;AACJ,MAAI,SAAA,CAAA,QAAe,IAAA,CAAA;AACjB,MAAA,SAAA,CAAA,MAAc,CAAI,CAAA;AAAiB,KAAA,CACrC;AAEE,IAAS,MAAA,YAAA,GAAY,MAAA;AAAW,MAClC,mBAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACA,KAAA,CAAA;AACA,IAAA,MAAA,MAAU,GAAM,MAAA;AAAA,MAClB,IAAA,CAAA,gBAAA,EAAA,IAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,MAAA,GAAA,MAAA;AAA4B,MAC9B,mBAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,MAAA,aAAe,CAAM,KAAA,GAAA,KAAA,CAAA;AACnB,MAAA,IAAA,CAAK,kBAAkB,KAAI,CAAA,CAAA;AAAA,KAC7B,CAAA;AAEA,IAAA,MAAM,UAAe,GAAA,MAAA;AACnB,MAAA,aAAA,CAAA,KAAA,GAA4B,IAAA,CAAA;AAC5B,KAAA,CAAA;AACA,IAAA,MAAA,oBAA4B;AAAA,MAC9B,aAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,WAAsB,GAAAP,YAAA,CAAA,MAAA;AAAA,MACxB,IAAA,SAAA,CAAA;AAEA,MAAA,IAAM,kBAAoB,EAAA;AACxB,QAAA,IAAA,aAAsB,CAAA,KAAA,CAAA,eAAA,EAAA;AAAA,UACxB,SAAA,GAAA,aAAA,CAAA,KAAA,CAAA,eAAA,EAAA,CAAA;AAEA,SAAM;AACJ,OAAI,MAAA;AACJ,QAAA,wBAAwB,CAAA,UAAA,CAAA,EAAA;AACtB,UAAI,SAAA,GAAA,gBAAqC,CAAA,GAAA,CAAA,CAAA,CAAA,KAAAU,eAAA,CAAA,CAAA,EAAA,KAAA,CAAA,WAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACvC,SAAY,MAAA;AAAoC,UAClD,SAAA,GAAAA,eAAA,CAAA,KAAA,CAAA,UAAA,EAAA,KAAA,CAAA,WAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,SACK;AACL,OAAI;AACF,MAAA,IAAA,mBAA6B,CAAA,qBAAA,EAAA;AAAA,QAAA,MAC3B,eAAU,GAAG,aAAM,CAAA,KAAa,sBAAU,CAAA,SAAA,CAAA,CAAA;AAAA,QAC5C,IAAA,CAAAC,qBAAA,CAAA,eAAA,EAAA,SAAA,CAAA,EAAA;AAAA,UACK,SAAA,GAAA,eAAA,CAAA;AACL,UAAA,IAAA,CAAA,kBAA4B,EAAA;AAAyC,YACvE,SAAA,CAAAC,qBAAA,CAAA,SAAA,CAAA,CAAA,CAAA;AAAA,WACF;AAEA,SAAI;AACF,OAAM;AAAsC,MAC1C,IAAAL,cAAA,CAAA,SAAA,CAAA,IAAA,SAAA,CAAA,IAAA,CAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,EAAA;AAAA,QACF,SAAA,GAAA,EAAA,CAAA;AACA,OAAA;AACE,MAAY,OAAA,SAAA,CAAA;AAGZ,KAAI,CAAA,CAAA;AACF,IAAU,MAAA,YAAA,GAAAP,YAAA,CAAA;AAA0B,MACtC,IAAA,CAAA,aAAA,CAAA,KAAA,CAAA,UAAA;AAAA,QACF,OAAA,EAAA,CAAA;AAAA,MACF,MAAA,cAAA,GAAA,mBAAA,CAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AACA,MAAI,IAAAO,cAAA,CAAQ,SAAU,CAAK,KAAA,CAAA,EAAA;AACzB,QAAA,OAAA;AAAa,UACf,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,IAAA,cAAA,IAAA,cAAA,CAAA,CAAA,CAAA,IAAA,EAAA;AACA,UAAO,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,IAAA,cAAA,IAAA,cAAA,CAAA,CAAA,CAAA,IAAA,EAAA;AAAA,SACR,CAAA;AAED,OAAM,MAAA,IAAA,oBAAyC,IAAA,EAAA;AAC7C,QAAA,OAAK,SAAA,CAAc,KAAM,CAAA;AACzB,OAAM;AACN,MAAI,IAAA,CAAA,YAAkB,CAAA,KAAA,IAAK,YAAG,CAAA,KAAA;AAC5B,QAAO,OAAA,EAAA,CAAA;AAAA,MAAA,IACL,cAAgB,CAAA,SAAyB,YAAA,CAAA,KAAA;AAAsB,QAAA;AACA,MACjE,IAAA,cAAA,EAAA;AAAA,QACF,OAAA,aAAqB,CAAA,KAAA,IAAgB,cAAA,CAAA,KAAA,IAAA,aAAA,CAAA,KAAA,GAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,cAAA,CAAA;AACnC,OAAA;AAAiB,MACnB,OAAA,EAAA,CAAA;AACA,KAAA,CAAA,CAAA;AACA,IAAA,MAAI,gBAAwB,GAAAP,YAAA,CAAA,MAAA,UAA2B,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AACvD,IAAA,MAAI,YAAgB,GAAAA,YAAA,CAAA,MAAA,KAAA,CAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAClB,IAAO,MAAA,aAAA,GAAAA,mBAAsC,KAAA,CAAA,IAAA,KAAA;AAEzC,IACN,MAAA,cAAA,GAAAA,YAAA,CAAA,MAAA,KAAA,CAAA,IAAA,KAAA,QAAA,CAAA,CAAA;AACA,IAAO,MAAA,aAAA,GAAAA,YAAA,CAAA,MAAA,KAAA,CAAA,IAAA,KAAA,OAAA,CAAA,CAAA;AAAA,IACT,MAAC,WAAA,GAAAA,YAAA,CAAA,MAAA,KAAA,CAAA,UAAA,KAAA,gBAAA,CAAA,KAAA,GAAAa,cAAA,GAAAC,iBAAA,CAAA,CAAA,CAAA;AAED,IAAA,MAAM,yBAA4B,CAAA,CAAA;AAElC,IAAA,MAAM,gBAAwB,GAAA,CAAA,KAAA,KAAM;AAEpC,MAAA,IAAM,KAAgB,CAAA,QAAA,IAAA,cAAe,CAAA;AAErC,QAAA,OAAuB;AAEvB,MAAA,IAAM,SAAgB,CAAA,KAAA,EAAA;AAEtB,QAAA,KAAoB,CAAA,eAAA,EAAA,CAAA;AAAA,QACZ,IAAA,aAAqB,CAAA,KAAA,CAAA,WAAA,EAAA;AAAiC,UAC9D,aAAA,CAAA,KAAA,CAAA,WAAA,EAAA,CAAA;AAEA,SAAM,MAAA;AAEN,UAAM,SAAA,CAAA,YAA0C,CAAA,KAAA,CAAA,CAAA;AAC9C,SAAI;AACJ,QAAA,uBAAqB,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AACnB,QAAA,SAAsB,CAAA,KAAA,GAAA,KAAA,CAAA;AAGtB,QAAI,MAAA,EAAA,CAAA;AACF,OAAA;AAAgC,MAAA,IAC3B,CAAA,OAAA,CAAA,CAAA;AACL,KAAA,CAAA;AAA4B,IAC9B,MAAA,YAAA,GAAAd,YAAA,CAAA,MAAA;AACA,MAAW,MAAA,EAAA,UAAA,EAAA,GAAA;AACX,MAAA,OAAA,CAAA,UAAkB,IAAAO,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA;AAClB,KAAO,CAAA,CAAA;AAAA,IACT,MAAA,gBAAA,GAAA,OAAA,KAAA,KAAA;AACA,MAAA,IAAA,EAAY,CAAA;AAAA,MACd,IAAA,KAAA,CAAA,QAAA,IAAA,cAAA,CAAA,KAAA;AAEA,QAAM,OAAA;AACJ,MAAM,IAAA,CAAA,CAAA,EAAE,eAAe,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,MAAA,OAAA,IAAA,SAAA,CAAA,KAAA,EAAA;AACvB,QACE,sBAAwB,IAAA,CAAA;AAA2C,OAEtE;AAED,KAAM,CAAA;AACJ,IAAI,MAAA,YAAkB,GAAA,MAAA;AACtB,MAAA,IAAK,KAAM,CAAA,QAAwB,IAAY,cAAA,CAAA;AAC7C,QAAA,OAAA;AAAsB,MACxB,IAAA,CAAA,YAAA,CAAA,KAAA,IAAA,KAAA,CAAA,SAAA,EAAA;AAAA,QACF,SAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACA,OAAA;AACE,KAAI,CAAA;AACJ,IAAA,MAAI,YAAC,GAAsB,MAAA;AACzB,MAAA,SAAA,CAAA,KAAkB,GAAA,KAAA,CAAA;AAAA,KACpB,CAAA;AAAA,IACF,MAAA,iBAAA,GAAA,CAAA,KAAA,KAAA;AACA,MAAA,IAAM;AACJ,MAAA,IAAA,KAAA,CAAU,QAAQ,IAAA,cAAA,CAAA,KAAA;AAAA,QACpB,OAAA;AAEA,MAAM,IAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,OAAoB,CAAC,CAAsB,CAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,MAAA,OAAA,IAAA,SAAA,CAAA,KAAA,EAAA;AAC/C,QAAI,aAAkB,CAAA,KAAA,GAAA,IAAA,CAAA;AACtB,OACG;AAGD,KAAA,CAAA;AAAsB,IACxB,MAAA,YAAA,GAAAP,YAAA,CAAA,MAAA;AAAA,MACF,OAAA,KAAA,CAAA,IAAA,CAAA,QAAA,CAAA,OAAA,CAAA,CAAA;AAEA,KAAM,CAAA,CAAA;AACJ,IAAO,MAAA,UAAW,GAAAe,8BAAgB,EAAA,CAAA;AAAA,IACpC,MAAC,QAAA,GAAAf,YAAA,CAAA,MAAA;AAED,MAAA,IAAM;AAEN,MAAA,gBAA0B,GAAAgB,SAAA,CAAA,eAAqB,IAAA,GAAA,YAAc,SAAU,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,CAAA;AAEvE,KAAA,CAAA,CAAA;AAAmB,IACjB,MAAA,UAAA,GAAAC,mBAAA,CAAA,QAAA,EAAA,CAAA,CAAA,KAAA;AAAA,MACA,MAAqB,eAAA,GAAAD,SAAA,CAAA,QAAA,CAAA,CAAA;AACnB,MAAM,MAAA,OAAA,GAAAE,0BAAgC,CAAA,CAAA;AACtC,MAAM,IAAA,4BAA+D,KAAA,eAAA,IAAA,CAAA,CAAA,YAAA,EAAA,CAAA,QAAA,CAAA,eAAA,CAAA,CAAA,IAAA,CAAA,CAAA,MAAA,KAAA,OAAA,IAAA,OAAA,IAAA,CAAA,CAAA,YAAA,EAAA,CAAA,QAAA,CAAA,OAAA,CAAA;AACrE,QAAA;AAOE,MAAA,aAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AACF,KAAA,CAAA,CAAA;AAAsB,IACxBC,mBAAA,CAAA,MAAA;AAAA,MACF,UAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,UAAA,EAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AACE,IAAa,MAAA,SAAA,GAAApB,OAAA,CAAA,IAAA,CAAA,CAAA;AAAA,IACf,MAAC,YAAA,GAAA,MAAA;AAED,MAAM,IAAA,SAAA,CAAA,OAA+B;AAErC,QAAA,mCAA2B,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AACzB,QAAA;AACE,UAAM,IAAA,YAA8B,CAAA,KAAA,CAAA,EAAA;AACpC,YAAI,SAAO,CAAAa,qBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACT,YAAI,SAAA,CAAA,KAAa,OAAQ,CAAA;AACvB,WAAU;AACV,SAAA;AAAkB,OACpB;AAAA,MACF,IAAA,SAAA,CAAA,KAAA,KAAA,EAAA,EAAA;AAAA,QACF,SAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AACA,QAAI,UAAA,CAAA,YAAwB,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAC1B,QAAA,SAAA,CAAU,aAAa;AACvB,OAAW;AACX,KAAA,CAAA;AAAkB,IACpB,MAAA,qBAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACF,IAAA,CAAA,KAAA;AAEA,QAAM,OAAA,IAAA,CAAA;AACJ,MAAI,oBAAe,CAAA,KAAA,CAAA,cAAA,CAAA,KAAA,CAAA,CAAA;AACnB,KAAO,CAAA;AAAyC,IAClD,MAAA,mBAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAM,IAAA,CAAA,KAAA;AACJ,QAAI,WAAe,CAAA;AACnB,MAAO,OAAA,aAAA,CAAc,KAAM,CAAA,cAAA,CAAgB,KAAK,CAAA,CAAA;AAAA,KAClD,CAAA;AAEA,IAAM,MAAA,YAAA,GAAe,CAAC,KAAqB,KAAA;AACzC,MAAO,OAAA,aAAA,CAAc,KAAM,CAAA,YAAA,CAAc,KAAK,CAAA,CAAA;AAAA,KAChD,CAAA;AAEA,IAAM,MAAA,kBAAA,GAAqB,OAAO,KAAiC,KAAA;AACjE,MAAI,IAAA,KAAA,CAAM,QAAY,IAAA,cAAA,CAAe,KAAO;AAE5C,QAAM;AACN,MAAA,MAAA,EAAA,IAAY,EAAsB,GAAA,KAAA,CAAA;AAClC,MAAI,WAAA,CAAA;AACF,MAAI,IAAA,IAAA,KAAAQ;AACF,QAAA,IAAA,aAAsB,CAAA,KAAA,KAAA,IAAA,EAAA;AACtB,UAAA,aAAqB,CAAA,KAAA,GAAA,KAAA,CAAA;AACrB,UAAA,KAAA,CAAM,cAAgB,EAAA,CAAA;AAAA,UACxB,KAAA,CAAA,eAAA,EAAA,CAAA;AACA,SAAA;AAAA,QACF,OAAA;AAEA,OAAI;AACF,MAAI,IAAA,IAAA,KAAAA,oBAAuC,EAAA;AACzC,QAAA,IAAA,aAAqB,CAAA,KAAA,CAAA,iBAAA,EAAA;AACrB,UAAA,KAAA,CAAM,cAAgB,EAAA,CAAA;AAAA,UACxB,KAAA,CAAA,eAAA,EAAA,CAAA;AACA,SAAI;AACF,QAAA,IAAA,aAAsB,CAAA,KAAA,KAAA,KAAA,EAAA;AACtB,UAAA,aAAe,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,UACjB,MAAAhB,YAAA,EAAA,CAAA;AACA,SAAI;AACF,QAAA,IAAA,mBAAsC,CAAA,iBAAA,EAAA;AACtC,UAAA,aAAA,CAAA,KAAA,CAAA,iBAAA,EAAA,CAAA;AAAA,UACF,OAAA;AAAA,SACF;AAEA,OAAI;AACF,MAAwB,IAAA,IAAA,KAAAgB,eAAA,CAAA,GAAA,EAAA;AACxB,QAAA,qBAAA,GAAA,IAAA,CAAA;AAAA,QACF,OAAA;AAEA,OAAA;AACE,MACE,IAAA,IAAA,KAAAA,eAAoB,CAAA,KAAA,IAAA,IACV,KAAAA,eAAA,CAAA,WACV,EAAA;AAEA,QAAa,IAAA,SAAA,CAAA,KAAA,KAAA,IAAA,IAAA,SAAA,CAAA,KAAA,KAAA,EAAA,IAAA,YAAA,CAAA,qBAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA,EAAA;AACb,UAAA,YAAA,EAAc,CAAQ;AAAA,UACxB,aAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AACA,SAAA;AACA,QAAA,KAAA,CAAA,eAAA,EAAA,CAAA;AAAA,QACF,OAAA;AAGA,OAAA;AACE,MAAA,IAAA,SAAsB,CAAA,KAAA,EAAA;AACtB,QAAA,KAAA,CAAA,eAAA,EAAA,CAAA;AAAA,QACF,OAAA;AACA,OAAI;AACF,MAAc,IAAA,aAAA,CAAA,wBAA+C,EAAA;AAAA,QAC/D,aAAA,CAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OACF;AACA,KAAM,CAAA;AACJ,IAAA,MAAA,WAAkB,GAAA,CAAA,CAAA,KAAA;AAGlB,MAAI,mBAAe,CAAO;AACxB,MAAA,IAAA,CAAA,aAAsB,CAAA,KAAA,EAAA;AAAA,QACxB,aAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,OACF;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,gBAAqB,GAAA,CAAA,KAAA,KAAA;AACrB,MAAA,eAAqB,KAAA,CAAA,MAAA,CAAA;AACnB,MAAA,IAAA,iBAAmB;AAAgC,QAC9C,SAAA,CAAA,KAAA,GAAA,CAAA,MAAA,CAAA,KAAA,EAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACL,OAAA,MAAA;AAAqC,QACvC,SAAA,CAAA,KAAA,GAAA,CAAA,MAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAAA,OACF;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,cAAqB,GAAA,CAAA,KAAA,KAAA;AACrB,MAAA,eAAqB,KAAA,CAAA,MAAA,CAAA;AACnB,MAAA,IAAA,iBAAmB;AAAgC,QAC9C,SAAA,CAAA,KAAA,GAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AACL,OAAA,MAAA;AAAqC,QACvC,SAAA,CAAA,KAAA,GAAA,CAAA,IAAA,EAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OACF;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,iBAAyB,GAAA,MAAA;AACzB,MAAA,IAAA,EAAM,CAAQ;AACd,MAAM,MAAA,MAAA,GAAA,SAAkB,CAAW,KAAA,CAAA;AACnC,MAAI,MAAA,KAAA,GAAe,qBAAW,CAAA,MAAA,IAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAC5B,MAAA,MAAA,SAAkB,GAAAJ,SAAA,CAAA,WAAA,CAAA,CAAA;AAAA,MAAA,IAChB,sBAAyB,EAAA,EAAA;AAAA,QACzB,SAAA,CAAA,KAAa,GAAQ;AAAM,UAC7B,mBAAA,CAAA,KAAA,CAAA;AACA,UAAA,CAAA,CAAA,EAAM,eAAY,CAAA,iBAAqB,KAAU,CAAA,GAAA,EAAC,QAAU,IAAA;AAC5D,SAAI,CAAA;AACF,QAAU,MAAA,QAAA,GAAA,CAAA,KAAA,EAAA,cAAyB,SAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA;AACnC,QAAA,IAAA,YAAkB,CAAA,QAAA,CAAA,EAAA;AAAA,UACpB,SAAA,CAAAJ,qBAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,UACF,SAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,SACF;AAEA,OAAA;AACE,KAAM,CAAA;AACN,IAAA,MAAA,eAAc,GAAA,MAAA;AACd,MAAM,IAAA,EAAA,CAAA;AACN,MAAI,MAAA,MAAA,GAAeI,SAAA,CAAA,SAAW,CAAA,CAAA;AAC5B,MAAA,MAAA,KAAA,GAAkB,qBAAA,CAAA,MAAA,IAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MAAA,MACV,SAAA,GAAAA,SAAgB,CAAA,WAAM,CAAA,CAAA;AAAA,MAAA,IAC5B,sBAAyB,EAAA,EAAA;AAAA,QAC3B,SAAA,CAAA,KAAA,GAAA;AACA,UAAA,CAAA,CAAA,EAAM,yBAAY,CAAA,KAAuB,IAAA,GAAA,KAAI,CAAK,GAAA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA;AAClD,UAAI,yBAAwB,CAAA;AAC1B,SAAU,CAAA;AACV,QAAA,MAAA,QAAkB,GAAA,CAAA,SAAA,IAAA,SAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA;AAAA,QACpB,IAAA,YAAA,CAAA,QAAA,CAAA,EAAA;AAAA,UACF,SAAA,CAAAJ,qBAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,UACF,SAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAEA,SAAM;AAEN,OAAM;AAGJ,KAAA,CAAA;AACA,IAAA,MAAA,gBAAoBb,OAAa,CAAA,EAAA,CAAA,CAAA;AAAA,IACnC,MAAA,iBAAA,GAAA,CAAA,CAAA,KAAA;AAGA,MAAM,aAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAA2B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACnD,MAAA,8BAAyB,GAAA,IAAA,CAAA;AAAA,KAC3B,CAAA;AAGA,IAAA,MAAM,gBAAgB,GAEpB,CAAA,CAAA,KAAA;AAGA,MAAK,IAAA,CAAA,iBAAuB,EAAA,CAAA,CAAA,CAAA;AAAU,KACxC,CAAA;AAEA,IAAA,MAAM,aAAc,GAAA,CAAA,KAAA,EAAA,IAAA,EAAA,IAAA,KAAA;AAClB,MAAA,IAAA,CAAA,cAAsB,EAAA,KAAA,EAAA,IAAA,EAAA,IAAA,CAAA,CAAA;AAAA,KACxB,CAAA;AAEA,IAAA,MAAM,QAAa,MAAA;AACjB,MAAA,IAAA,EAAA,CAAA;AAAqB,MACvB,CAAA,EAAA,GAAA,QAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AAEA,KAAA,CAAA;AAAmC,IACjC,MAAA,IAAA,GAAA,MAAA;AAAA,MACD,IAAA,EAAA,CAAA;AAED,MAAa,CAAA,EAAA,GAAA,QAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,EAAA,CAAA;AAAA,KAAA,CAAA;AAAA,IAAAsB,WAAA,CAAAC,mCAAA,EAAA;AAAA,MAAA,KAAA;AAAA,KAIX,CAAA,CAAA;AAAA,IAAA,MAAA,CAAA;AAAA,MAAA,KAAA;AAAA,MAAA,IAAA;AAAA,MAIA,UAAA;AAAA,MAAA,WAAA;AAAA,MAAA,MAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAIA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAC,aAAA,EAAA,EAAAC,eAAA,CAAAC,SAAA,CAAAC,iBAAA,CAAA,EAAAC,cAAA,CAAA;AAAA,QAAA,OAAA,EAAA,WAAA;AAAA,QAAA,GAAA,EAAA,SAAA;AAAA,QAIA,OAAA,EAAA,aAAA,CAAA,KAAA;AAAA,QAAA,MAAA,EAAA,OAAA;AAAA,QAAA,IAAA,EAAA,EAAA;AAAA,QAAA,OAAA,EAAA,OAAA;AAAA,OAIA,EAAA,IAAA,CAAA,MAAA,EAAA;AAAA,QACD,IAAA,EAAA,QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}