/**
 * 路由缓存管理器 - 防闪烁核心机制
 */
class RouteCache {
  constructor() {
    this.CACHE_KEY = 'route_cache'
    this.AUTH_CACHE_KEY = 'auth_cache'
    this.LAYOUT_CACHE_KEY = 'layout_cache'
    this.cache = new Map()
    this.initialized = false
    
    // 从 sessionStorage 恢复缓存
    this.restoreCache()
  }

  /**
   * 缓存认证状态和路由信息
   */
  cacheAuthState(authData) {
    const cacheData = {
      isAuthenticated: authData.isAuthenticated,
      user: authData.user,
      token: authData.token,
      timestamp: Date.now(),
      routes: authData.routes || []
    }
    
    // 存储到内存缓存
    this.cache.set(this.AUTH_CACHE_KEY, cacheData)
    
    // 存储到 sessionStorage（页面刷新时保持）
    try {
      sessionStorage.setItem(this.AUTH_CACHE_KEY, JSON.stringify(cacheData))
      console.log('RouteCache: 认证状态已缓存', cacheData)
    } catch (error) {
      console.warn('RouteCache: 缓存认证状态失败', error)
    }
  }

  /**
   * 获取缓存的认证状态
   */
  getCachedAuthState() {
    // 优先从内存缓存获取
    let cached = this.cache.get(this.AUTH_CACHE_KEY)
    
    if (!cached) {
      // 从 sessionStorage 恢复
      try {
        const stored = sessionStorage.getItem(this.AUTH_CACHE_KEY)
        if (stored) {
          cached = JSON.parse(stored)
          this.cache.set(this.AUTH_CACHE_KEY, cached)
        }
      } catch (error) {
        console.warn('RouteCache: 恢复认证缓存失败', error)
      }
    }
    
    // 检查缓存是否过期（5分钟）
    if (cached && Date.now() - cached.timestamp > 5 * 60 * 1000) {
      console.log('RouteCache: 认证缓存已过期')
      this.clearAuthCache()
      return null
    }
    
    return cached
  }

  /**
   * 缓存布局状态
   */
  cacheLayoutState(path, layout) {
    const layoutData = {
      path,
      layout,
      timestamp: Date.now()
    }
    
    this.cache.set(`${this.LAYOUT_CACHE_KEY}_${path}`, layoutData)
    
    try {
      const allLayouts = this.getAllCachedLayouts()
      allLayouts[path] = layoutData
      sessionStorage.setItem(this.LAYOUT_CACHE_KEY, JSON.stringify(allLayouts))
    } catch (error) {
      console.warn('RouteCache: 缓存布局状态失败', error)
    }
  }

  /**
   * 获取缓存的布局状态
   */
  getCachedLayout(path) {
    // 优先从内存缓存获取
    let cached = this.cache.get(`${this.LAYOUT_CACHE_KEY}_${path}`)
    
    if (!cached) {
      // 从 sessionStorage 恢复
      try {
        const allLayouts = this.getAllCachedLayouts()
        cached = allLayouts[path]
        if (cached) {
          this.cache.set(`${this.LAYOUT_CACHE_KEY}_${path}`, cached)
        }
      } catch (error) {
        console.warn('RouteCache: 恢复布局缓存失败', error)
      }
    }
    
    return cached?.layout
  }

  /**
   * 获取所有缓存的布局
   */
  getAllCachedLayouts() {
    try {
      const stored = sessionStorage.getItem(this.LAYOUT_CACHE_KEY)
      return stored ? JSON.parse(stored) : {}
    } catch (error) {
      return {}
    }
  }

  /**
   * 预缓存路由组件
   */
  async preloadRouteComponents(routes) {
    console.log('RouteCache: 开始预加载路由组件')
    
    const preloadPromises = routes.map(async (route) => {
      try {
        if (route.component && typeof route.component === 'function') {
          const component = await route.component()
          this.cache.set(`component_${route.name}`, component)
          console.log(`RouteCache: 预加载组件 ${route.name} 成功`)
        }
      } catch (error) {
        console.warn(`RouteCache: 预加载组件 ${route.name} 失败`, error)
      }
    })
    
    await Promise.allSettled(preloadPromises)
    console.log('RouteCache: 路由组件预加载完成')
  }

  /**
   * 获取预加载的组件
   */
  getPreloadedComponent(routeName) {
    return this.cache.get(`component_${routeName}`)
  }

  /**
   * 智能预测下一个可能访问的路由
   */
  predictNextRoute(currentPath, userRole) {
    const predictions = {
      '/': userRole ? '/students' : '/login',
      '/login': userRole ? '/students' : '/login',
      '/students': '/students'
    }
    
    return predictions[currentPath] || currentPath
  }

  /**
   * 恢复所有缓存
   */
  restoreCache() {
    try {
      // 恢复认证缓存
      const authCache = sessionStorage.getItem(this.AUTH_CACHE_KEY)
      if (authCache) {
        const authData = JSON.parse(authCache)
        this.cache.set(this.AUTH_CACHE_KEY, authData)
      }
      
      // 恢复布局缓存
      const layoutCache = sessionStorage.getItem(this.LAYOUT_CACHE_KEY)
      if (layoutCache) {
        const layoutData = JSON.parse(layoutCache)
        Object.entries(layoutData).forEach(([path, data]) => {
          this.cache.set(`${this.LAYOUT_CACHE_KEY}_${path}`, data)
        })
      }
      
      this.initialized = true
      console.log('RouteCache: 缓存恢复完成')
    } catch (error) {
      console.warn('RouteCache: 缓存恢复失败', error)
      this.initialized = true
    }
  }

  /**
   * 清除认证缓存
   */
  clearAuthCache() {
    this.cache.delete(this.AUTH_CACHE_KEY)
    sessionStorage.removeItem(this.AUTH_CACHE_KEY)
    console.log('RouteCache: 认证缓存已清除')
  }

  /**
   * 清除所有缓存
   */
  clearAllCache() {
    this.cache.clear()
    sessionStorage.removeItem(this.AUTH_CACHE_KEY)
    sessionStorage.removeItem(this.LAYOUT_CACHE_KEY)
    console.log('RouteCache: 所有缓存已清除')
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return {
      memoryCache: this.cache.size,
      authCached: this.cache.has(this.AUTH_CACHE_KEY),
      initialized: this.initialized,
      sessionStorageUsed: {
        auth: !!sessionStorage.getItem(this.AUTH_CACHE_KEY),
        layout: !!sessionStorage.getItem(this.LAYOUT_CACHE_KEY)
      }
    }
  }
}

// 创建全局缓存实例
const routeCache = new RouteCache()

export default routeCache
