import authService from './AuthService.js'
import routeCache from '@/utils/RouteCache.js'

/**
 * 缓存增强的认证服务 - 防闪烁优化
 */
class CachedAuthService {
  constructor() {
    this.baseService = authService
    this.cache = routeCache
    this.initPromise = null
  }

  /**
   * 快速获取认证状态（优先使用缓存）
   */
  getQuickAuthState() {
    console.log('CachedAuthService: 快速获取认证状态')
    
    // 1. 尝试从缓存获取
    const cached = this.cache.getCachedAuthState()
    if (cached) {
      console.log('CachedAuthService: 使用缓存的认证状态', {
        isAuthenticated: cached.isAuthenticated,
        user: cached.user?.name,
        cacheAge: Date.now() - cached.timestamp
      })
      return {
        isAuthenticated: cached.isAuthenticated,
        user: cached.user,
        token: cached.token,
        fromCache: true
      }
    }
    
    // 2. 缓存未命中，从 localStorage 获取
    const token = this.baseService.getToken()
    const userInfo = this.baseService.getUserInfoFromStorage()
    
    const authState = {
      isAuthenticated: !!(token && userInfo),
      user: userInfo,
      token: token,
      fromCache: false
    }
    
    // 3. 缓存新的认证状态
    if (authState.isAuthenticated) {
      this.cache.cacheAuthState(authState)
    }
    
    console.log('CachedAuthService: 从存储获取认证状态', authState)
    return authState
  }

  /**
   * 异步验证并更新缓存
   */
  async validateAndUpdateCache() {
    if (this.initPromise) {
      return this.initPromise
    }

    this.initPromise = this._performValidation()
    return this.initPromise
  }

  async _performValidation() {
    try {
      console.log('CachedAuthService: 开始异步验证认证状态')
      
      const quickState = this.getQuickAuthState()
      
      if (!quickState.isAuthenticated) {
        console.log('CachedAuthService: 用户未登录，跳过验证')
        return quickState
      }

      // 异步验证 token 有效性（不阻塞页面渲染）
      try {
        const isValid = await this.baseService.checkLoginStatus()
        
        if (isValid) {
          console.log('CachedAuthService: Token 验证成功，更新缓存')
          this.cache.cacheAuthState({
            ...quickState,
            timestamp: Date.now()
          })
        } else {
          console.log('CachedAuthService: Token 验证失败，清除缓存')
          this.cache.clearAuthCache()
          this.baseService.clearAuth()
          return {
            isAuthenticated: false,
            user: null,
            token: null,
            fromCache: false
          }
        }
      } catch (error) {
        console.warn('CachedAuthService: Token 验证失败', error)
        // 验证失败但不清除缓存，允许离线使用
      }

      return quickState
    } catch (error) {
      console.error('CachedAuthService: 验证过程出错', error)
      return this.getQuickAuthState()
    } finally {
      this.initPromise = null
    }
  }

  /**
   * 登录并缓存状态
   */
  async login(credentials) {
    try {
      console.log('CachedAuthService: 开始登录')
      
      const userData = await this.baseService.login(credentials)
      
      // 缓存登录状态
      const authState = {
        isAuthenticated: true,
        user: userData,
        token: userData.token,
        routes: this.getAvailableRoutes(userData.role)
      }
      
      this.cache.cacheAuthState(authState)
      
      // 预加载可能访问的路由组件
      this.preloadUserRoutes(userData.role)
      
      console.log('CachedAuthService: 登录成功，状态已缓存')
      return userData
    } catch (error) {
      console.error('CachedAuthService: 登录失败', error)
      throw error
    }
  }

  /**
   * 登出并清除缓存
   */
  async logout() {
    try {
      console.log('CachedAuthService: 开始登出')
      
      await this.baseService.logout()
      
      // 清除所有缓存
      this.cache.clearAllCache()
      
      console.log('CachedAuthService: 登出成功，缓存已清除')
    } catch (error) {
      console.error('CachedAuthService: 登出失败', error)
      // 即使登出失败也清除缓存
      this.cache.clearAllCache()
      throw error
    }
  }

  /**
   * 根据用户角色获取可用路由
   */
  getAvailableRoutes(userRole) {
    const routeMap = {
      'ADMIN': ['/students', '/teachers', '/courses'],
      'TEACHER': ['/students', '/courses'],
      'STUDENT': ['/students']
    }
    
    return routeMap[userRole] || ['/students']
  }

  /**
   * 预加载用户可能访问的路由组件
   */
  async preloadUserRoutes(userRole) {
    const routes = this.getAvailableRoutes(userRole)
    
    console.log('CachedAuthService: 预加载用户路由', routes)
    
    // 异步预加载，不阻塞主流程
    setTimeout(async () => {
      try {
        const routeConfigs = routes.map(path => ({
          name: path.substring(1), // 移除开头的 '/'
          component: () => import(`@/views/${this.getComponentName(path)}.vue`)
        }))
        
        await this.cache.preloadRouteComponents(routeConfigs)
      } catch (error) {
        console.warn('CachedAuthService: 预加载路由失败', error)
      }
    }, 100)
  }

  /**
   * 根据路径获取组件名称
   */
  getComponentName(path) {
    const componentMap = {
      '/students': 'StudentManagement',
      '/teachers': 'TeacherManagement',
      '/courses': 'CourseManagement'
    }
    
    return componentMap[path] || 'StudentManagement'
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return this.cache.getCacheStats()
  }

  /**
   * 预热缓存（应用启动时调用）
   */
  warmupCache() {
    console.log('CachedAuthService: 开始缓存预热')
    
    // 立即获取认证状态并缓存
    const authState = this.getQuickAuthState()
    
    if (authState.isAuthenticated) {
      // 预加载用户路由
      this.preloadUserRoutes(authState.user?.role)
      
      // 异步验证（不阻塞）
      this.validateAndUpdateCache()
    }
    
    console.log('CachedAuthService: 缓存预热完成')
    return authState
  }
}

// 创建全局实例
const cachedAuthService = new CachedAuthService()

export default cachedAuthService
