{"version": 3, "file": "content.js", "sources": ["../../../../../../packages/components/tooltip/src/content.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { popperContentProps } from '@element-plus/components/popper'\nimport { useAriaProps, useDelayedToggleProps } from '@element-plus/hooks'\nimport { teleportProps } from '@element-plus/components/teleport'\n\nimport type TooltipContent from './content.vue'\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport const useTooltipContentProps = buildProps({\n  ...useDelayedToggleProps,\n  ...popperContentProps,\n  /**\n   * @description which element the tooltip CONTENT appends to\n   */\n  appendTo: {\n    type: teleportProps.to.type,\n  },\n  /**\n   * @description display content, can be overridden by `slot#content`\n   */\n  content: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description whether `content` is treated as HTML string\n   */\n  rawContent: Boolean,\n  /**\n   * @description when tooltip inactive and `persistent` is `false` , popconfirm will be destroyed\n   */\n  persistent: Boolean,\n  // because model toggle prop is generated dynamically\n  // so the typing cannot be evaluated by typescript as type:\n  // [name]: { type: <PERSON>olean, default: null }\n  // so we need to declare that again for type checking.\n  /**\n   * @description visibility of Tooltip\n   */\n  visible: {\n    type: definePropType<boolean | null>(Boolean),\n    default: null,\n  },\n  /**\n   * @description animation name\n   */\n  transition: String,\n  /**\n   * @description whether tooltip content is teleported, if `true` it will be teleported to where `append-to` sets\n   */\n  teleported: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether Tooltip is disabled\n   */\n  disabled: Boolean,\n  ...useAriaProps(['ariaLabel']),\n} as const)\n\nexport type ElTooltipContentProps = ExtractPropTypes<\n  typeof useTooltipContentProps\n>\nexport type ElTooltipContentPropsPublic = __ExtractPublicPropTypes<\n  typeof useTooltipContentProps\n>\n\nexport type TooltipContentInstance = InstanceType<typeof TooltipContent> &\n  unknown\n"], "names": ["buildProps", "useDelayedToggleProps", "popperContentProps", "teleportProps", "definePropType", "useAriaProps"], "mappings": ";;;;;;;;;;AAIY,MAAC,sBAAsB,GAAGA,kBAAU,CAAC;AACjD,EAAE,GAAGC,2BAAqB;AAC1B,EAAE,GAAGC,0BAAkB;AACvB,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAEC,sBAAa,CAAC,EAAE,CAAC,IAAI;AAC/B,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAEC,sBAAc,CAAC,OAAO,CAAC;AACjC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,UAAU,EAAE,MAAM;AACpB,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,GAAGC,oBAAY,CAAC,CAAC,WAAW,CAAC,CAAC;AAChC,CAAC;;;;"}