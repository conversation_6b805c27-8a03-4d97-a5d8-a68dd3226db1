{"version": 3, "file": "tree.js", "sources": ["../../../../../../packages/components/tree-select/src/tree.ts"], "sourcesContent": ["// @ts-nocheck\nimport { computed, nextTick, toRefs, watch } from 'vue'\nimport { isEqual, isNil, pick } from 'lodash-unified'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { escapeStringRegexp, isEmpty, isFunction } from '@element-plus/utils'\nimport ElTree from '@element-plus/components/tree'\nimport TreeSelectOption from './tree-select-option'\nimport {\n  isValidArray,\n  isValidValue,\n  toValidArray,\n  treeEach,\n  treeFind,\n} from './utils'\n\nimport type { CacheOption } from './cache-options'\nimport type { Ref } from 'vue'\nimport type ElSelect from '@element-plus/components/select'\nimport type Node from '@element-plus/components/tree/src/model/node'\nimport type { TreeNodeData } from '@element-plus/components/tree/src/tree.type'\nimport type { TreeInstance } from '@element-plus/components/tree'\n\nexport const useTree = (\n  props,\n  { attrs, slots, emit },\n  {\n    select,\n    tree,\n    key,\n  }: {\n    select: Ref<InstanceType<typeof ElSelect> | undefined>\n    tree: Ref<TreeInstance | undefined>\n    key: Ref<string>\n  }\n) => {\n  watch(\n    [() => props.modelValue, tree],\n    () => {\n      if (props.showCheckbox) {\n        nextTick(() => {\n          const treeInstance = tree.value\n          if (\n            treeInstance &&\n            !isEqual(\n              treeInstance.getCheckedKeys(),\n              toValidArray(props.modelValue)\n            )\n          ) {\n            treeInstance.setCheckedKeys(toValidArray(props.modelValue))\n          }\n        })\n      }\n    },\n    {\n      immediate: true,\n      deep: true,\n    }\n  )\n\n  const propsMap = computed(() => ({\n    value: key.value,\n    label: 'label',\n    children: 'children',\n    disabled: 'disabled',\n    isLeaf: 'isLeaf',\n    ...props.props,\n  }))\n\n  const getNodeValByProp = (\n    prop: 'value' | 'label' | 'children' | 'disabled' | 'isLeaf',\n    data: TreeNodeData\n  ) => {\n    const propVal = propsMap.value[prop]\n    if (isFunction(propVal)) {\n      return propVal(\n        data,\n        tree.value?.getNode(getNodeValByProp('value', data)) as Node\n      )\n    } else {\n      return data[propVal as string]\n    }\n  }\n\n  const defaultExpandedParentKeys = toValidArray(props.modelValue)\n    .map((value) => {\n      return treeFind(\n        props.data || [],\n        (data) => getNodeValByProp('value', data) === value,\n        (data) => getNodeValByProp('children', data),\n        (data, index, array, parent) =>\n          parent && getNodeValByProp('value', parent)\n      )\n    })\n    .filter((item) => isValidValue(item))\n\n  const cacheOptions = computed(() => {\n    if (!props.renderAfterExpand && !props.lazy) return []\n\n    const options: CacheOption[] = []\n\n    treeEach(\n      props.data.concat(props.cacheData),\n      (node) => {\n        const value = getNodeValByProp('value', node)\n        options.push({\n          value,\n          currentLabel: getNodeValByProp('label', node),\n          isDisabled: getNodeValByProp('disabled', node),\n        })\n      },\n      (data) => getNodeValByProp('children', data)\n    )\n\n    return options\n  })\n\n  const getChildCheckedKeys = () => {\n    return tree.value?.getCheckedKeys().filter((checkedKey) => {\n      const node = tree.value?.getNode(checkedKey) as Node\n      return !isNil(node) && isEmpty(node.childNodes)\n    })\n  }\n\n  return {\n    ...pick(toRefs(props), Object.keys(ElTree.props)),\n    ...attrs,\n    nodeKey: key,\n\n    // only expand on click node when the `check-strictly` is false\n    expandOnClickNode: computed(() => {\n      return !props.checkStrictly && props.expandOnClickNode\n    }),\n\n    // show current selected node only first time,\n    // fix the problem of expanding multiple nodes when checking multiple nodes\n    defaultExpandedKeys: computed(() => {\n      return props.defaultExpandedKeys\n        ? props.defaultExpandedKeys.concat(defaultExpandedParentKeys)\n        : defaultExpandedParentKeys\n    }),\n\n    renderContent: (h, { node, data, store }) => {\n      return h(\n        TreeSelectOption,\n        {\n          value: getNodeValByProp('value', data),\n          label: getNodeValByProp('label', data),\n          disabled: getNodeValByProp('disabled', data),\n          visible: node.visible,\n        },\n        props.renderContent\n          ? () => props.renderContent(h, { node, data, store })\n          : slots.default\n          ? () => slots.default({ node, data, store })\n          : undefined\n      )\n    },\n    filterNodeMethod: (value, data, node) => {\n      if (props.filterNodeMethod)\n        return props.filterNodeMethod(value, data, node)\n      if (!value) return true\n      const regexp = new RegExp(escapeStringRegexp(value), 'i')\n      return regexp.test(getNodeValByProp('label', data) || '')\n    },\n    onNodeClick: (data, node, e) => {\n      attrs.onNodeClick?.(data, node, e)\n\n      // `onCheck` is trigger when `checkOnClickNode`\n      if (props.showCheckbox && props.checkOnClickNode) return\n\n      // now `checkOnClickNode` is false, only no checkbox and `checkStrictly` or `isLeaf`\n      if (!props.showCheckbox && (props.checkStrictly || node.isLeaf)) {\n        if (!getNodeValByProp('disabled', data)) {\n          const option = select.value?.states.options.get(\n            getNodeValByProp('value', data)\n          )\n          select.value?.handleOptionSelect(option)\n        }\n      } else if (props.expandOnClickNode) {\n        e.proxy.handleExpandIconClick()\n      }\n      select.value?.focus()\n    },\n    onCheck: (data, params) => {\n      // ignore when no checkbox, like only `checkOnClickNode` is true\n      if (!props.showCheckbox) return\n\n      const dataValue = getNodeValByProp('value', data)\n      const dataMap = {}\n      treeEach(\n        [tree.value.store.root],\n        (node) => (dataMap[node.key] = node),\n        (node) => node.childNodes\n      )\n\n      // fix: checkedKeys has not cached keys\n      const uncachedCheckedKeys = params.checkedKeys\n      const cachedKeys = props.multiple\n        ? toValidArray(props.modelValue).filter(\n            (item) => !(item in dataMap) && !uncachedCheckedKeys.includes(item)\n          )\n        : []\n      const checkedKeys = cachedKeys.concat(uncachedCheckedKeys)\n\n      if (props.checkStrictly) {\n        emit(\n          UPDATE_MODEL_EVENT,\n          // Checking for changes may come from `check-on-node-click`\n          props.multiple\n            ? checkedKeys\n            : checkedKeys.includes(dataValue)\n            ? dataValue\n            : undefined\n        )\n      }\n      // only can select leaf node\n      else {\n        if (props.multiple) {\n          const childKeys = getChildCheckedKeys()\n\n          emit(UPDATE_MODEL_EVENT, cachedKeys.concat(childKeys))\n        } else {\n          // select first leaf node when check parent\n          const firstLeaf = treeFind(\n            [data],\n            (data) =>\n              !isValidArray(getNodeValByProp('children', data)) &&\n              !getNodeValByProp('disabled', data),\n            (data) => getNodeValByProp('children', data)\n          )\n          const firstLeafKey = firstLeaf\n            ? getNodeValByProp('value', firstLeaf)\n            : undefined\n\n          // unselect when any child checked\n          const hasCheckedChild =\n            isValidValue(props.modelValue) &&\n            !!treeFind(\n              [data],\n              (data) => getNodeValByProp('value', data) === props.modelValue,\n              (data) => getNodeValByProp('children', data)\n            )\n\n          emit(\n            UPDATE_MODEL_EVENT,\n            firstLeafKey === props.modelValue || hasCheckedChild\n              ? undefined\n              : firstLeafKey\n          )\n        }\n      }\n\n      nextTick(() => {\n        const checkedKeys = toValidArray(props.modelValue)\n        tree.value.setCheckedKeys(checkedKeys)\n\n        attrs.onCheck?.(data, {\n          checkedKeys: tree.value.getCheckedKeys(),\n          checkedNodes: tree.value.getCheckedNodes(),\n          halfCheckedKeys: tree.value.getHalfCheckedKeys(),\n          halfCheckedNodes: tree.value.getHalfCheckedNodes(),\n        })\n      })\n\n      select.value?.focus()\n    },\n\n    onNodeExpand: (data, node, e) => {\n      attrs.onNodeExpand?.(data, node, e)\n      nextTick(() => {\n        if (\n          !props.checkStrictly &&\n          props.lazy &&\n          props.multiple &&\n          node.checked\n        ) {\n          const dataMap = {}\n          const uncachedCheckedKeys = (\n            tree.value as TreeInstance\n          ).getCheckedKeys()\n\n          treeEach(\n            [tree.value.store.root],\n            (node) => (dataMap[node.key] = node),\n            (node) => node.childNodes\n          )\n\n          const cachedKeys = toValidArray(props.modelValue).filter(\n            (item) => !(item in dataMap) && !uncachedCheckedKeys.includes(item)\n          )\n\n          const childKeys = getChildCheckedKeys()\n          emit(UPDATE_MODEL_EVENT, cachedKeys.concat(childKeys))\n        }\n      })\n    },\n    // else\n    cacheOptions,\n  }\n}\n"], "names": ["watch", "nextTick", "isEqual", "to<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "computed", "isFunction", "treeFind", "isValidValue", "treeEach", "isNil", "isEmpty", "pick", "toRefs", "ElTree", "TreeSelectOption", "escapeStringRegexp", "UPDATE_MODEL_EVENT", "isValidArray"], "mappings": ";;;;;;;;;;;;;;AAaY,MAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;AACvD,EAAE,MAAM;AACR,EAAE,IAAI;AACN,EAAE,GAAG;AACL,CAAC,KAAK;AACN,EAAEA,SAAK,CAAC,CAAC,MAAM,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,MAAM;AAC9C,IAAI,IAAI,KAAK,CAAC,YAAY,EAAE;AAC5B,MAAMC,YAAQ,CAAC,MAAM;AACrB,QAAQ,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;AACxC,QAAQ,IAAI,YAAY,IAAI,CAACC,qBAAO,CAAC,YAAY,CAAC,cAAc,EAAE,EAAEC,kBAAY,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE;AACrG,UAAU,YAAY,CAAC,cAAc,CAACA,kBAAY,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;AACtE,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG,EAAE;AACL,IAAI,SAAS,EAAE,IAAI;AACnB,IAAI,IAAI,EAAE,IAAI;AACd,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAGC,YAAQ,CAAC,OAAO;AACnC,IAAI,KAAK,EAAE,GAAG,CAAC,KAAK;AACpB,IAAI,KAAK,EAAE,OAAO;AAClB,IAAI,QAAQ,EAAE,UAAU;AACxB,IAAI,QAAQ,EAAE,UAAU;AACxB,IAAI,MAAM,EAAE,QAAQ;AACpB,IAAI,GAAG,KAAK,CAAC,KAAK;AAClB,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,MAAM,gBAAgB,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK;AAC3C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACzC,IAAI,IAAIC,iBAAU,CAAC,OAAO,CAAC,EAAE;AAC7B,MAAM,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7G,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;AAC3B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,yBAAyB,GAAGF,kBAAY,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK;AAClF,IAAI,OAAOG,cAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,KAAK,EAAE,CAAC,IAAI,KAAK,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,KAAK,MAAM,IAAI,gBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;AACtN,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAKC,kBAAY,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1C,EAAE,MAAM,YAAY,GAAGH,YAAQ,CAAC,MAAM;AACtC,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,IAAI,CAAC,KAAK,CAAC,IAAI;AAC/C,MAAM,OAAO,EAAE,CAAC;AAChB,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC;AACvB,IAAII,cAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,KAAK;AAC3D,MAAM,MAAM,KAAK,GAAG,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACpD,MAAM,OAAO,CAAC,IAAI,CAAC;AACnB,QAAQ,KAAK;AACb,QAAQ,YAAY,EAAE,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC;AACrD,QAAQ,UAAU,EAAE,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC;AACtD,OAAO,CAAC,CAAC;AACT,KAAK,EAAE,CAAC,IAAI,KAAK,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;AACrD,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,mBAAmB,GAAG,MAAM;AACpC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,UAAU,KAAK;AAC3F,MAAM,IAAI,GAAG,CAAC;AACd,MAAM,MAAM,IAAI,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACjF,MAAM,OAAO,CAACC,mBAAK,CAAC,IAAI,CAAC,IAAIC,aAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACtD,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,GAAGC,kBAAI,CAACC,UAAM,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,IAAI,CAACC,YAAM,CAAC,KAAK,CAAC,CAAC;AACrD,IAAI,GAAG,KAAK;AACZ,IAAI,OAAO,EAAE,GAAG;AAChB,IAAI,iBAAiB,EAAET,YAAQ,CAAC,MAAM;AACtC,MAAM,OAAO,CAAC,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,iBAAiB,CAAC;AAC7D,KAAK,CAAC;AACN,IAAI,mBAAmB,EAAEA,YAAQ,CAAC,MAAM;AACxC,MAAM,OAAO,KAAK,CAAC,mBAAmB,GAAG,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,yBAAyB,CAAC,GAAG,yBAAyB,CAAC;AACjI,KAAK,CAAC;AACN,IAAI,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK;AACjD,MAAM,OAAO,CAAC,CAACU,2BAAgB,EAAE;AACjC,QAAQ,KAAK,EAAE,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC;AAC9C,QAAQ,KAAK,EAAE,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC;AAC9C,QAAQ,QAAQ,EAAE,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC;AACpD,QAAQ,OAAO,EAAE,IAAI,CAAC,OAAO;AAC7B,OAAO,EAAE,KAAK,CAAC,aAAa,GAAG,MAAM,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAC1J,KAAK;AACL,IAAI,gBAAgB,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,KAAK;AAC7C,MAAM,IAAI,KAAK,CAAC,gBAAgB;AAChC,QAAQ,OAAO,KAAK,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACzD,MAAM,IAAI,CAAC,KAAK;AAChB,QAAQ,OAAO,IAAI,CAAC;AACpB,MAAM,MAAM,MAAM,GAAG,IAAI,MAAM,CAACC,0BAAkB,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;AAChE,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AAChE,KAAK;AACL,IAAI,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,KAAK;AACpC,MAAM,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACzB,MAAM,CAAC,EAAE,GAAG,KAAK,CAAC,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAChF,MAAM,IAAI,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,gBAAgB;AACtD,QAAQ,OAAO;AACf,MAAM,IAAI,CAAC,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;AACvE,QAAQ,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE;AACjD,UAAU,MAAM,MAAM,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;AACvH,UAAU,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;AAC/E,SAAS;AACT,OAAO,MAAM,IAAI,KAAK,CAAC,iBAAiB,EAAE;AAC1C,QAAQ,CAAC,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;AACxC,OAAO;AACP,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;AACxD,KAAK;AACL,IAAI,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK;AAC/B,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,IAAI,CAAC,KAAK,CAAC,YAAY;AAC7B,QAAQ,OAAO;AACf,MAAM,MAAM,SAAS,GAAG,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACxD,MAAM,MAAM,OAAO,GAAG,EAAE,CAAC;AACzB,MAAMP,cAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,CAAC;AACvG,MAAM,MAAM,mBAAmB,GAAG,MAAM,CAAC,WAAW,CAAC;AACrD,MAAM,MAAM,UAAU,GAAG,KAAK,CAAC,QAAQ,GAAGL,kBAAY,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,EAAE,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;AAC1J,MAAM,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;AACjE,MAAM,IAAI,KAAK,CAAC,aAAa,EAAE;AAC/B,QAAQ,IAAI,CAACa,wBAAkB,EAAE,KAAK,CAAC,QAAQ,GAAG,WAAW,GAAG,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC;AACtH,OAAO,MAAM;AACb,QAAQ,IAAI,KAAK,CAAC,QAAQ,EAAE;AAC5B,UAAU,MAAM,SAAS,GAAG,mBAAmB,EAAE,CAAC;AAClD,UAAU,IAAI,CAACA,wBAAkB,EAAE,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;AACjE,SAAS,MAAM;AACf,UAAU,MAAM,SAAS,GAAGV,cAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,KAAK,CAACW,kBAAY,CAAC,gBAAgB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,gBAAgB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;AACpM,UAAU,MAAM,YAAY,GAAG,SAAS,GAAG,gBAAgB,CAAC,OAAO,EAAE,SAAS,CAAC,GAAG,KAAK,CAAC,CAAC;AACzF,UAAU,MAAM,eAAe,GAAGV,kBAAY,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAACD,cAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,KAAK,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,KAAK,CAAC,UAAU,EAAE,CAAC,KAAK,KAAK,gBAAgB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;AACzM,UAAU,IAAI,CAACU,wBAAkB,EAAE,YAAY,KAAK,KAAK,CAAC,UAAU,IAAI,eAAe,GAAG,KAAK,CAAC,GAAG,YAAY,CAAC,CAAC;AACjH,SAAS;AACT,OAAO;AACP,MAAMf,YAAQ,CAAC,MAAM;AACrB,QAAQ,IAAI,GAAG,CAAC;AAChB,QAAQ,MAAM,YAAY,GAAGE,kBAAY,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AAC5D,QAAQ,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;AAChD,QAAQ,CAAC,GAAG,GAAG,KAAK,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE;AACvE,UAAU,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;AAClD,UAAU,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;AACpD,UAAU,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE;AAC1D,UAAU,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE;AAC5D,SAAS,CAAC,CAAC;AACX,OAAO,CAAC,CAAC;AACT,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;AACxD,KAAK;AACL,IAAI,YAAY,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,KAAK;AACrC,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,CAAC,EAAE,GAAG,KAAK,CAAC,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACjF,MAAMF,YAAQ,CAAC,MAAM;AACrB,QAAQ,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE;AAClF,UAAU,MAAM,OAAO,GAAG,EAAE,CAAC;AAC7B,UAAU,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;AAClE,UAAUO,cAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE,CAAC,KAAK,KAAK,KAAK,CAAC,UAAU,CAAC,CAAC;AAChH,UAAU,MAAM,UAAU,GAAGL,kBAAY,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,EAAE,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AACxI,UAAU,MAAM,SAAS,GAAG,mBAAmB,EAAE,CAAC;AAClD,UAAU,IAAI,CAACa,wBAAkB,EAAE,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;AACjE,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,YAAY;AAChB,GAAG,CAAC;AACJ;;;;"}