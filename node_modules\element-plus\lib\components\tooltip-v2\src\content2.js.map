{"version": 3, "file": "content2.js", "sources": ["../../../../../../packages/components/tooltip-v2/src/content.vue"], "sourcesContent": ["<template>\n  <div ref=\"contentRef\" :style=\"contentStyle\" data-tooltip-v2-root>\n    <div v-if=\"!nowrap\" :data-side=\"side\" :class=\"contentClass\">\n      <slot :content-style=\"contentStyle\" :content-class=\"contentClass\" />\n      <el-visually-hidden :id=\"contentId\" role=\"tooltip\">\n        <template v-if=\"ariaLabel\">\n          {{ ariaLabel }}\n        </template>\n        <slot v-else />\n      </el-visually-hidden>\n      <slot name=\"arrow\" :style=\"arrowStyle\" :side=\"side\" />\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, inject, onMounted, provide, ref, unref, watch } from 'vue'\nimport { offset } from '@floating-ui/dom'\nimport {\n  arrowMiddleware,\n  useFloating,\n  useNamespace,\n  useZIndex,\n} from '@element-plus/hooks'\nimport ElVisuallyHidden from '@element-plus/components/visual-hidden'\nimport { tooltipV2ContentKey, tooltipV2RootKey } from './constants'\nimport { tooltipV2ContentProps } from './content'\nimport { tooltipV2CommonProps } from './common'\n\nimport type { TooltipV2Sides } from './common'\nimport type { CSSProperties } from 'vue'\nimport type { Middleware } from '@floating-ui/dom'\n\ndefineOptions({\n  name: 'ElTooltipV2Content',\n})\n\nconst props = defineProps({ ...tooltipV2ContentProps, ...tooltipV2CommonProps })\n\nconst { triggerRef, contentId } = inject(tooltipV2RootKey)!\n\nconst placement = ref(props.placement)\nconst strategy = ref(props.strategy)\nconst arrowRef = ref<HTMLElement | null>(null)\n\nconst { referenceRef, contentRef, middlewareData, x, y, update } = useFloating({\n  placement,\n  strategy,\n  middleware: computed(() => {\n    const middleware: Middleware[] = [offset(props.offset)]\n\n    if (props.showArrow) {\n      middleware.push(\n        arrowMiddleware({\n          arrowRef,\n        })\n      )\n    }\n\n    return middleware\n  }),\n})\n\nconst zIndex = useZIndex().nextZIndex()\n\nconst ns = useNamespace('tooltip-v2')\n\nconst side = computed(() => {\n  return placement.value.split('-')[0] as TooltipV2Sides\n})\n\nconst contentStyle = computed<CSSProperties>(() => {\n  return {\n    position: unref(strategy),\n    top: `${unref(y) || 0}px`,\n    left: `${unref(x) || 0}px`,\n    zIndex,\n  }\n})\n\nconst arrowStyle = computed<CSSProperties>(() => {\n  if (!props.showArrow) return {}\n\n  const { arrow } = unref(middlewareData)\n\n  return {\n    [`--${ns.namespace.value}-tooltip-v2-arrow-x`]: `${arrow?.x}px` || '',\n    [`--${ns.namespace.value}-tooltip-v2-arrow-y`]: `${arrow?.y}px` || '',\n  }\n})\n\nconst contentClass = computed(() => [\n  ns.e('content'),\n  ns.is('dark', props.effect === 'dark'),\n  ns.is(unref(strategy)),\n  props.contentClass,\n])\n\nwatch(arrowRef, () => update())\n\nwatch(\n  () => props.placement,\n  (val) => (placement.value = val)\n)\n\nonMounted(() => {\n  watch(\n    () => props.reference || triggerRef.value,\n    (el) => {\n      referenceRef.value = el || undefined\n    },\n    {\n      immediate: true,\n    }\n  )\n})\n\nprovide(tooltipV2ContentKey, { arrowRef })\n</script>\n"], "names": ["inject", "tooltipV2RootKey", "ref", "useFloating", "computed", "offset", "arrowMiddleware", "useZIndex", "useNamespace", "unref", "watch", "onMounted", "provide", "tooltipV2ContentKey", "_openBlock", "_createElementBlock", "_normalizeStyle", "_unref"], "mappings": ";;;;;;;;;;;;;;;uCAiCc,CAAA;AAAA,EACZ,IAAM,EAAA,oBAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAA,MAAM,EAAE,UAAA,EAAY,SAAU,EAAA,GAAIA,WAAOC,0BAAgB,CAAA,CAAA;AAEzD,IAAM,MAAA,SAAA,GAAYC,OAAI,CAAA,KAAA,CAAM,SAAS,CAAA,CAAA;AACrC,IAAM,MAAA,QAAA,GAAWA,OAAI,CAAA,KAAA,CAAM,QAAQ,CAAA,CAAA;AACnC,IAAM,MAAA,QAAA,GAAWA,QAAwB,IAAI,CAAA,CAAA;AAE7C,IAAM,MAAA,EAAE,cAAc,UAAY,EAAA,cAAA,EAAgB,GAAG,CAAG,EAAA,MAAA,KAAWC,iBAAY,CAAA;AAAA,MAC7E,SAAA;AAAA,MACA,QAAA;AAAA,MACA,UAAA,EAAYC,aAAS,MAAM;AACzB,QAAA,MAAM,UAA2B,GAAA,CAACC,UAAO,CAAA,KAAA,CAAM,MAAM,CAAC,CAAA,CAAA;AAEtD,QAAA,IAAI,MAAM,SAAW,EAAA;AACnB,UAAW,UAAA,CAAA,IAAA,CAAAC,qBAAA,CAAA;AAAA,YACT,QAAgB;AAAA,WACd,CAAA,CAAA,CAAA;AAAA,SAAA;AACD,QACH,OAAA,UAAA,CAAA;AAAA,OACF,CAAA;AAEA,KAAO,CAAA,CAAA;AAAA,IAAA,MACR,MAAA,GAAAC,iBAAA,EAAA,CAAA,UAAA,EAAA,CAAA;AAAA,IACH,MAAC,EAAA,GAAAC,oBAAA,CAAA,YAAA,CAAA,CAAA;AAED,IAAM,MAAA,IAAA,GAAAJ,YAAmB,CAAA,MAAa;AAEtC,MAAM,OAAA,eAAkB,CAAY,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEpC,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,YAAiB,GAAAA,YAAY,CAAA,MAAK;AAAC,MACpC,OAAA;AAED,QAAM,QAAA,EAAAK,SAAA,CAAA;AACJ,QAAO,GAAA,EAAA,CAAA,EAAAA,SAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA;AAAA,QACL,IAAA,EAAA,CAAA,EAAAA,WAAgB,CAAQ,IAAA,CAAA,CAAA,EAAA,CAAA;AAAA,QACxB,MAAK;AAAgB,OAAA,CACrB;AAAsB,KACtB,CAAA,CAAA;AAAA,IACF,MAAA,UAAA,GAAAL,YAAA,CAAA,MAAA;AAAA,MACD,IAAA,CAAA,KAAA,CAAA,SAAA;AAED,QAAM,OAAA,EAAA,CAAA;AACJ,MAAA,MAAK,EAAA,KAAiB,EAAA,GAAAK,SAAA,CAAA,cAAQ,CAAA,CAAA;AAE9B,MAAA,OAAQ;AAER,QAAO,CAAA,CAAA,EAAA,EAAA,EAAA,CAAA,SAAA,CAAA,KAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,EAAA,KAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,EAAA;AAAA,QACL,CAAC,CAAK,EAAA,EAAA,EAAA,CAAG,SAAU,CAAA,KAAK,qBAAqB,GAAG,CAAA,EAAG,KAAO,IAAS,IAAA,GAAA,KAAA,CAAA,GAAA,KAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,EAAA;AAAA,OACnE,CAAA;AAAmE,KACrE,CAAA,CAAA;AAAA,IACF,MAAC,YAAA,GAAAL,YAAA,CAAA,MAAA;AAED,MAAM,EAAA,CAAA,CAAA,CAAA,SAAA,CAAA;AAA8B,MAClC,EAAA,CAAG,EAAE,CAAS,MAAA,EAAA,KAAA,CAAA,MAAA,KAAA,MAAA,CAAA;AAAA,MACd,EAAG,CAAA,EAAA,CAAGK,SAAQ,CAAA,QAAM;AAAiB,MACrC,KAAG,CAAG,YAAM;AAAS,KAAA,CACrB,CAAM;AAAA,IACRC,SAAC,CAAA,QAAA,EAAA,MAAA,MAAA,EAAA,CAAA,CAAA;AAED,IAAMA,SAAA,CAAA,MAAA,KAAgB,CAAA,SAAA,EAAO,CAAC,GAAA,KAAA,SAAA,CAAA,KAAA,GAAA,GAAA,CAAA,CAAA;AAE9B,IAAAC,aAAA,CAAA,MAAA;AAAA,MACED,UAAM,MAAM,KAAA,CAAA,SAAA,IAAA,UAAA,CAAA,KAAA,EAAA,CAAA,EAAA,KAAA;AAAA,QACF,YAAA,CAAA,KAAkB,GAAA,EAAA,IAAA,KAAA,CAAA,CAAA;AAAA,OAC9B,EAAA;AAEA,QAAA,SAAgB,EAAA,IAAA;AACd,OAAA,CAAA,CAAA;AAAA,KACE,CAAA,CAAA;AAAoC,IAAAE,WAC5B,CAAAC,6BAAA,EAAA,EAAA,QAAA,EAAA,CAAA,CAAA;AACN,IAAA,OAAA,CAAA,IAAA,EAAA;AAA2B,MAC7B,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,QACA,OAAA,EAAA,YAAA;AAAA,QAAA,GACa,EAAA,UAAA;AAAA,QACb,KAAA,EAAAC,kBAAA,CAAAC,SAAA,CAAA,YAAA,CAAA,CAAA;AAAA,QACF,sBAAA,EAAA,EAAA;AAAA,OACD,EAAA;AAED,QAAQ,CAAA,IAAA,CAAA,MAAA,IAAAH,aAAuB,EAAA,EAAAC,sBAAU,CAAA,KAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}